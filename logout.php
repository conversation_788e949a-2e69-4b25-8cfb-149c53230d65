<?php
/**
 * Logout Handler
 * Securely logs out the user and redirects to login page
 */

require_once 'includes/auth.php';

// Verify CSRF token if provided
if (isset($_GET['token'])) {
    if (!Auth::verifyCSRFToken($_GET['token'])) {
        // Invalid token, but still logout for security
        Auth::logout();
        header('Location: login.php?error=invalid_token');
        exit;
    }
}

// Logout the user
Auth::logout();

// Redirect to login page with success message
header('Location: login.php?message=logged_out');
exit;
?>
