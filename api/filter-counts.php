<?php
/**
 * API endpoint to get updated filter counts
 * Returns current counts for all filter categories
 */

require_once __DIR__ . '/../config/config.php';

// Set JSON header
header('Content-Type: application/json');

try {
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
        DB_USER,
        DB_PASSWORD,
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );

    // Get filter counts with the same logic as index.php
    $filterCountsStmt = $pdo->query("
        SELECT
            SUM(CASE WHEN p.external_url IS NULL OR p.external_url = '' THEN 1 ELSE 0 END) as empty_count,
            SUM(CASE WHEN (url_override.rule IS NOT NULL OR status_override.rule IS NOT NULL) THEN 1 ELSE 0 END) as manual_count,
            SUM(CASE WHEN ((p.external_url_status = 'down' AND (status_override.rule IS NULL OR status_override.rule != 'up')) OR status_override.rule = 'down') THEN 1 ELSE 0 END) as down_count,
            SUM(CASE WHEN ((p.external_url_status = 'down' AND (status_override.rule IS NULL OR status_override.rule != 'up')) OR status_override.rule = 'down') AND pr.reviewed_at IS NULL THEN 1 ELSE 0 END) as not_reviewed_count,
            SUM(CASE WHEN p.url_checked = 1 THEN 1 ELSE 0 END) as checked_count,
            SUM(CASE WHEN (p.url_checked IS NULL OR p.url_checked = 0) THEN 1 ELSE 0 END) as not_checked_count
        FROM products p
        LEFT JOIN overrides url_override ON p.id = url_override.product_id AND url_override.type = 'url'
        LEFT JOIN overrides status_override ON p.id = status_override.product_id AND status_override.type = 'status'
        LEFT JOIN product_reviews pr ON p.id = pr.product_id
    ");
    $filterCounts = $filterCountsStmt->fetch();

    // Get total products count
    $totalStmt = $pdo->query("SELECT COUNT(*) as total_products FROM products");
    $stats = $totalStmt->fetch();

    // Return the counts
    echo json_encode([
        'success' => true,
        'counts' => [
            'total' => intval($stats['total_products']),
            'empty' => intval($filterCounts['empty_count']),
            'manual' => intval($filterCounts['manual_count']),
            'down' => intval($filterCounts['down_count']),
            'not_reviewed' => intval($filterCounts['not_reviewed_count']),
            'checked' => intval($filterCounts['checked_count']),
            'not_checked' => intval($filterCounts['not_checked_count'])
        ]
    ]);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Database error: ' . $e->getMessage()
    ]);
}
?>
