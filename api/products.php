<?php
/**
 * Server-side DataTables API endpoint for products
 * Handles pagination, sorting, searching, and filtering
 */

require_once __DIR__ . '/../config/config.php';

// Set JSON header
header('Content-Type: application/json');

try {
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
        DB_USER,
        DB_PASSWORD,
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );

    // Handle both GET and POST requests for testing
    $request = $_REQUEST;

    // Get DataTables parameters
    $draw = intval($request['draw'] ?? 1);
    $start = intval($request['start'] ?? 0);
    $length = intval($request['length'] ?? 25);
    $searchValue = $request['search']['value'] ?? '';

    // Get ordering parameters
    $orderColumn = intval($request['order'][0]['column'] ?? 3); // Default to votes
    $orderDir = $request['order'][0]['dir'] ?? 'desc';
    
    // Column mapping (must match the order in DataTable)
    $columns = [
        0 => 'p.name',           // Product
        1 => 'p.tagline',        // Tagline  
        2 => 'p.description',    // Description
        3 => 'p.votes_count',    // Votes
        4 => 'p.comments_count', // Comments
        5 => 'p.review_rating',  // Rating
        6 => 'p.date_created',   // Launch Date
        7 => 'categories',       // Categories
        8 => 'p.url',           // Website
        9 => 'p.external_url_status', // URL Checked
        10 => 'p.created_at',    // Created
        11 => 'p.updated_at'     // Updated
    ];
    
    $orderBy = $columns[$orderColumn] ?? 'p.votes_count';
    
    // Get filter parameters
    $statusFilter = $request['statusFilter'] ?? '';
    $dateFilter = $request['dateFilter'] ?? '';
    
    // Build base query
    $baseQuery = "
        FROM products p
        LEFT JOIN product_categories pc ON p.id = pc.product_id
        LEFT JOIN categories c ON pc.category_id = c.id
        LEFT JOIN overrides url_override ON p.id = url_override.product_id AND url_override.type = 'url'
        LEFT JOIN overrides status_override ON p.id = status_override.product_id AND status_override.type = 'status'
        LEFT JOIN product_reviews pr ON p.id = pr.product_id
    ";
    
    // Build WHERE conditions
    $whereConditions = [];
    $params = [];
    
    // Search condition
    if (!empty($searchValue)) {
        $whereConditions[] = "(
            p.name LIKE ? OR
            p.tagline LIKE ? OR
            p.description LIKE ? OR
            c.name LIKE ? OR
            p.url LIKE ? OR
            p.external_url LIKE ? OR
            url_override.rule LIKE ?
        )";
        $searchParam = '%' . $searchValue . '%';
        $params = array_merge($params, [$searchParam, $searchParam, $searchParam, $searchParam, $searchParam, $searchParam, $searchParam]);
    }
    
    // Status filter
    if (!empty($statusFilter)) {
        switch ($statusFilter) {
            case 'empty':
                $whereConditions[] = "(p.external_url IS NULL OR p.external_url = '')";
                break;
            case 'manual':
                $whereConditions[] = "(url_override.rule IS NOT NULL OR status_override.rule IS NOT NULL)";
                break;
            case 'down':
                $whereConditions[] = "((p.external_url_status = 'down' AND (status_override.rule IS NULL OR status_override.rule != 'up')) OR status_override.rule = 'down')";
                break;
            case 'not_reviewed':
                $whereConditions[] = "((p.external_url_status = 'down' AND (status_override.rule IS NULL OR status_override.rule != 'up')) OR status_override.rule = 'down') AND pr.reviewed_at IS NULL";
                break;
            case 'checked':
                $whereConditions[] = "p.url_checked = 1";
                break;
            case 'not_checked':
                $whereConditions[] = "(p.url_checked IS NULL OR p.url_checked = 0)";
                break;
        }
    }
    
    // Date filter
    if (!empty($dateFilter)) {
        $whereConditions[] = "DATE(p.date_created) = ?";
        $params[] = $dateFilter;
    }
    
    $whereClause = '';
    if (!empty($whereConditions)) {
        $whereClause = 'WHERE ' . implode(' AND ', $whereConditions);
    }
    
    // OPTIMIZED: Get total records with simple query (no JOINs needed for count)
    $totalQuery = "SELECT COUNT(*) FROM products";
    $stmt = $pdo->prepare($totalQuery);
    $stmt->execute();
    $totalRecords = $stmt->fetchColumn();

    // OPTIMIZED: Get filtered records count with minimal JOINs
    if (empty($whereConditions)) {
        $filteredRecords = $totalRecords;
    } else {
        // Only include JOINs that are actually needed for the WHERE clause
        $simpleBaseQuery = "FROM products p";

        // Add JOINs only if needed by filters
        if (strpos($whereClause, 'url_override') !== false || strpos($whereClause, 'status_override') !== false) {
            $simpleBaseQuery .= " LEFT JOIN overrides url_override ON p.id = url_override.product_id AND url_override.type = 'url'";
            $simpleBaseQuery .= " LEFT JOIN overrides status_override ON p.id = status_override.product_id AND status_override.type = 'status'";
        }
        if (strpos($whereClause, 'pr.') !== false) {
            $simpleBaseQuery .= " LEFT JOIN product_reviews pr ON p.id = pr.product_id";
        }

        $filteredQuery = "SELECT COUNT(DISTINCT p.id) " . $simpleBaseQuery . " " . $whereClause;
        $stmt = $pdo->prepare($filteredQuery);
        $stmt->execute($params);
        $filteredRecords = $stmt->fetchColumn();
    }
    
    // Get the actual data - OPTIMIZED: Get products first, then categories
    // Step 1: Get the main product data without GROUP_CONCAT
    $productQuery = "
        SELECT
            p.*,
            url_override.rule as manual_url,
            url_override.created_at as manual_url_created_at,
            status_override.rule as status_override,
            MAX(pr.reviewed_at) as reviewed_at
        " . $baseQuery . "
        " . $whereClause . "
        GROUP BY p.id, url_override.rule, url_override.created_at, status_override.rule
        ORDER BY " . $orderBy . " " . $orderDir . "
        LIMIT $length OFFSET $start
    ";

    $stmt = $pdo->prepare($productQuery);
    $stmt->execute($params);
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Step 2: Get categories for just these products (much faster)
    if (!empty($products)) {
        $productIds = array_column($products, 'id');
        $placeholders = str_repeat('?,', count($productIds) - 1) . '?';

        $categoryQuery = "
            SELECT
                pc.product_id,
                GROUP_CONCAT(DISTINCT c.name ORDER BY c.name SEPARATOR ', ') as categories
            FROM product_categories pc
            JOIN categories c ON pc.category_id = c.id
            WHERE pc.product_id IN ($placeholders)
            GROUP BY pc.product_id
        ";

        $stmt = $pdo->prepare($categoryQuery);
        $stmt->execute($productIds);
        $categories = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);

        // Step 3: Merge categories into products
        foreach ($products as &$product) {
            $product['categories'] = $categories[$product['id']] ?? '';
        }
    }

    // Format data for DataTables
    $data = [];
    foreach ($products as $product) {
        $data[] = [
            'id' => $product['id'],
            'name' => $product['name'],
            'tagline' => $product['tagline'] ?? '',
            'description' => $product['description'] ?? '',
            'votes_count' => intval($product['votes_count']),
            'comments_count' => intval($product['comments_count'] ?? 0),
            'review_rating' => $product['review_rating'] ? floatval($product['review_rating']) : null,
            'date_created' => $product['date_created'],
            'categories' => $product['categories'] ?? '',
            'url' => $product['url'] ?? '',
            'product_url' => $product['product_url'] ?? '',
            'external_url' => $product['external_url'] ?? '',
            'external_url_status' => $product['external_url_status'] ?? '',
            'url_checked' => $product['url_checked'],
            'manual_url' => $product['manual_url'] ?? '',
            'manual_url_created_at' => $product['manual_url_created_at'] ?? '',
            'status_override' => $product['status_override'] ?? '',
            'reviewed_at' => $product['reviewed_at'] ?? null,
            'created_at' => $product['created_at'],
            'updated_at' => $product['updated_at']
        ];
    }
    
    // Return DataTables response
    echo json_encode([
        'draw' => $draw,
        'recordsTotal' => $totalRecords,
        'recordsFiltered' => $filteredRecords,
        'data' => $data
    ]);

} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'error' => 'Database error: ' . $e->getMessage()
    ]);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => 'Server error: ' . $e->getMessage()
    ]);
}
?>
