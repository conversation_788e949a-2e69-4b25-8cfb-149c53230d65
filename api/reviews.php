<?php
/**
 * Product Reviews API
 * Handles marking and unmarking products as reviewed
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/auth.php';

header('Content-Type: application/json');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

// Check authentication using the Auth class
if (!Auth::isAuthenticated()) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

try {
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
        DB_USER,
        DB_PASSWORD,
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );

    $action = $_GET['action'] ?? '';
    $productId = $_GET['product_id'] ?? '';

    if (empty($productId)) {
        http_response_code(400);
        echo json_encode(['error' => 'Product ID is required']);
        exit;
    }

    // Verify product exists
    $stmt = $pdo->prepare("SELECT id FROM products WHERE id = ?");
    $stmt->execute([$productId]);
    if (!$stmt->fetch()) {
        http_response_code(404);
        echo json_encode(['error' => 'Product not found']);
        exit;
    }

    switch ($action) {
        case 'mark':
            // Mark product as reviewed
            $stmt = $pdo->prepare("
                INSERT INTO product_reviews (product_id) 
                VALUES (?) 
                ON DUPLICATE KEY UPDATE reviewed_at = CURRENT_TIMESTAMP
            ");
            $stmt->execute([$productId]);
            
            // Get the review date for response
            $stmt = $pdo->prepare("SELECT reviewed_at FROM product_reviews WHERE product_id = ?");
            $stmt->execute([$productId]);
            $review = $stmt->fetch();
            
            echo json_encode([
                'success' => true,
                'message' => 'Product marked as reviewed',
                'reviewed_at' => $review['reviewed_at']
            ]);
            break;

        case 'unmark':
            // Remove review status
            $stmt = $pdo->prepare("DELETE FROM product_reviews WHERE product_id = ?");
            $stmt->execute([$productId]);
            
            echo json_encode([
                'success' => true,
                'message' => 'Review status removed'
            ]);
            break;

        default:
            http_response_code(400);
            echo json_encode(['error' => 'Invalid action. Use "mark" or "unmark"']);
            break;
    }

} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Database error: ' . $e->getMessage()]);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Server error: ' . $e->getMessage()]);
}
?>
