<?php
/**
 * Product Hunt Products Display
 * Web interface for viewing and filtering products
 */

require_once 'config/config.php';
require_once 'includes/auth.php';

// Require authentication
Auth::requireAuth();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Product Hunt Dashboard</title>
    
    <!-- DataTables CSS -->
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.7/css/jquery.dataTables.min.css">

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="https://<?php echo $_SERVER['HTTP_HOST']; ?>/assets/img/favicon.png">

    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            font-size: 13px;
        }
        .container {
            max-width: none;
        }
        h1 {
            text-align: left;
            font-size: 24px;
        }
        .stats {
            margin-bottom: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .stats-left {
            display: flex;
            align-items: center;
        }

        /* Make table horizontally scrollable */
        .dataTables_wrapper {
            overflow-x: auto;
            width: 100%;
        }

        .dataTables_scroll {
            overflow-x: auto;
        }

        .dataTables_scrollBody {
            overflow-x: auto !important;
        }

        table.dataTable {
            width: 100% !important;
            font-size: 13px;
            table-layout: fixed !important;
        }

        table.dataTable td,
        table.dataTable th {
            vertical-align: top !important;
        }

        /* Manual URL popup styles */
        .popup-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            z-index: 1000;
        }

        .popup-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            width: 450px;
            max-width: 90%;
            font-family: Arial, sans-serif;
            font-size: 13px;
        }

        .popup-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
            margin: 0;
        }

        .popup-header h3 {
            margin: 0;
            font-size: 16px;
            font-weight: bold;
        }

        .popup-close {
            cursor: pointer;
            font-size: 20px;
            color: #333;
            line-height: 1;
            padding: 5px 10px;
            background: #f0f0f0;
            border: 1px solid #ccc;
        }

        .popup-close:hover {
            color: #333;
            background: #e0e0e0;
        }

        .popup-form {
            padding: 20px;
        }

        .popup-form label {
            display: block;
            margin-bottom: 8px;
            font-weight: normal;
        }

        .popup-form input[type="url"] {
            width: 100%;
            padding: 8px 10px;
            border: 1px solid #ccc;
            margin-bottom: 20px;
            font-size: 13px;
            box-sizing: border-box;
        }

        .popup-buttons {
            text-align: right;
            margin: 0;
        }

        .popup-buttons button {
            margin-left: 8px;
            padding: 8px 16px;
            border: 1px solid #ccc;
            background: #f0f0f0;
            cursor: pointer;
            font-size: 13px;
        }

        .popup-buttons button:hover {
            background: #e0e0e0;
        }

        .manual-url-notice {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 8px;
            margin: 5px 0;
            font-size: 12px;
        }

        /* Temporary notice styles */
        .temp-notice {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #f8f9fa;
            border: 1px solid #ccc;
            padding: 12px 16px;
            font-size: 13px;
            font-family: Arial, sans-serif;
            z-index: 1001;
            max-width: 300px;
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.3s ease;
        }

        .temp-notice.show {
            opacity: 1;
            transform: translateY(0);
        }

        .temp-notice.success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }

        .temp-notice.error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }

        /* Force fixed column widths */
        table.dataTable th:nth-child(1), table.dataTable td:nth-child(1) { width: 180px !important; max-width: 180px !important; font-weight: bold !important; }
        table.dataTable th:nth-child(2), table.dataTable td:nth-child(2) { width: 120px !important; max-width: 120px !important; }
        table.dataTable th:nth-child(3), table.dataTable td:nth-child(3) { width: 180px !important; max-width: 180px !important; }
        table.dataTable th:nth-child(4), table.dataTable td:nth-child(4) { width: 40px !important; max-width: 40px !important; text-align: center !important; }
        table.dataTable th:nth-child(5), table.dataTable td:nth-child(5) { width: 50px !important; max-width: 50px !important; text-align: center !important; }
        table.dataTable th:nth-child(6), table.dataTable td:nth-child(6) { width: 40px !important; max-width: 40px !important; text-align: center !important; }
        table.dataTable th:nth-child(7), table.dataTable td:nth-child(7) { width: 60px !important; max-width: 60px !important; text-align: center !important; }
        table.dataTable th:nth-child(8), table.dataTable td:nth-child(8) { width: 120px !important; max-width: 120px !important; }
        table.dataTable th:nth-child(9), table.dataTable td:nth-child(9) { width: 200px !important; max-width: 200px !important; }
        table.dataTable th:nth-child(10), table.dataTable td:nth-child(10) { width: 70px !important; max-width: 70px !important; text-align: center !important; }
        table.dataTable th:nth-child(11), table.dataTable td:nth-child(11) { width: 80px !important; max-width: 80px !important; text-align: center !important; }
        table.dataTable th:nth-child(12), table.dataTable td:nth-child(12) { width: 80px !important; max-width: 80px !important; text-align: center !important; }



        /* Text truncation for long content */
        .text-truncate {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .text-center {
            text-align: center;
        }

        /* URL link styling to prevent overflow */
        .url-link {
            color: #0066cc;
            word-break: break-all;
            overflow-wrap: break-word;
            hyphens: auto;
            max-width: 100%;
        }

        /* Specific styling for Links column */
        table.dataTable td:nth-child(9) {
            word-break: break-all;
            overflow-wrap: break-word;
            white-space: normal !important;
        }

        table.dataTable td a {
            color: #0066cc;
        }

        /* Limit description to 3 lines with ellipsis */
        .product-description {
            white-space: normal;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
            min-width: 200px;
        }

        .filter-container a {
            text-decoration: none;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="container">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
            <h1 style="margin: 0;"><a href="https://<?php echo $_SERVER['HTTP_HOST']; ?>" style="text-decoration: none; color: inherit;">Product Hunt Dashboard</a></h1>
            <div style="display: flex; align-items: center; gap: 15px;">
                <span>Welcome, <?= htmlspecialchars(Auth::getUsername(), ENT_QUOTES, 'UTF-8') ?></span>
                <a href="logout.php?token=<?= htmlspecialchars(Auth::generateCSRFToken(), ENT_QUOTES, 'UTF-8') ?>" style="padding: 4px 8px; text-decoration: none; border: 1px solid #ccc; color: #333; background: #f0f0f0; font-size: 13px;">
                   Logout
                </a>
            </div>
        </div>
        
        <?php
        try {
            $dsn = "mysql:host=" . DB_HOST . ";port=" . DB_PORT . ";dbname=" . DB_NAME . ";charset=utf8mb4";
            $pdo = new PDO($dsn, DB_USER, DB_PASSWORD, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
            ]);
            
            // Get statistics
            $statsStmt = $pdo->query("
                SELECT
                    COUNT(*) as total_products,
                    COUNT(DISTINCT date_created) as unique_dates,
                    MIN(date_created) as oldest_fetch_date,
                    MAX(date_created) as newest_fetch_date
                FROM products
            ");
            $stats = $statsStmt->fetch();

            // Get filter counts
            $filterCountsStmt = $pdo->query("
                SELECT
                    SUM(CASE WHEN p.external_url IS NULL OR p.external_url = '' THEN 1 ELSE 0 END) as Empty_count,
                    SUM(CASE WHEN (url_override.rule IS NOT NULL OR status_override.rule IS NOT NULL) THEN 1 ELSE 0 END) as manual_count,
                    SUM(CASE WHEN ((p.external_url_status = 'down' AND (status_override.rule IS NULL OR status_override.rule != 'up')) OR status_override.rule = 'down') THEN 1 ELSE 0 END) as down_count,
                    SUM(CASE WHEN ((p.external_url_status = 'down' AND (status_override.rule IS NULL OR status_override.rule != 'up')) OR status_override.rule = 'down') AND pr.reviewed_at IS NULL THEN 1 ELSE 0 END) as not_reviewed_count,
                    SUM(CASE WHEN p.url_checked = 1 THEN 1 ELSE 0 END) as checked_count,
                    SUM(CASE WHEN (p.url_checked IS NULL OR p.url_checked = 0) THEN 1 ELSE 0 END) as not_checked_count
                FROM products p
                LEFT JOIN overrides url_override ON p.id = url_override.product_id AND url_override.type = 'url'
                LEFT JOIN overrides status_override ON p.id = status_override.product_id AND status_override.type = 'status'
                LEFT JOIN product_reviews pr ON p.id = pr.product_id
            ");
            $filterCounts = $filterCountsStmt->fetch();

            // Get available dates for the date picker
            $datesStmt = $pdo->prepare("SELECT DISTINCT DATE(date_created) as date FROM products ORDER BY date DESC");
            $datesStmt->execute();
            $availableDates = $datesStmt->fetchAll(PDO::FETCH_COLUMN);

            // No longer loading all products - will be loaded via AJAX for better performance
            // This improves page load time from 30+ seconds to < 1 second with 200k+ products
            
        } catch (PDOException $e) {
            echo "<div style='color: red; text-align: center;'>Database error: " . htmlspecialchars($e->getMessage()) . "</div>";
            exit;
        }
        ?>
        
        <!-- Statistics -->
        <div class="stats">
            <div class="stats-left">
                <span>Total Products: <?= number_format($stats['total_products']) ?></span>
                <span style="margin: 0 10px; color: #ccc;">|</span>
                <span>Launch Days: <?= $stats['unique_dates'] ?></span>
                <span style="margin: 0 10px; color: #ccc;">|</span>
                <span>Newest Fetch: <?= $stats['newest_fetch_date'] ? date('M j, Y', strtotime($stats['newest_fetch_date'])) : '-' ?></span>
                <span style="margin: 0 10px; color: #ccc;">|</span>
                <span>Oldest Fetch: <?= $stats['oldest_fetch_date'] ? date('M j, Y', strtotime($stats['oldest_fetch_date'])) : '-' ?></span>
            </div>
            <div class="live-time">
                <span id="localTime">Loading...</span>
                <span style="margin: 0 10px; color: #ccc;">|</span>
                <span id="pacificTime">Loading...</span>
            </div>
        </div>

        <!-- Status Filters -->
        <div class="filter-container" style="margin: 0 0 15px; display: flex; align-items: center; flex-wrap: wrap; gap: 5px;">
            <a href="javascript:void(0);" id="filterAll" class="filter-link active" style="margin: 0 5px 0 0; font-weight: bold;">All Products (<?= number_format($stats['total_products']) ?>)</a>
            <a href="javascript:void(0);" id="filterEmpty" class="filter-link" style="margin: 0 5px;">Empty (<?= number_format($filterCounts['Empty_count']) ?>)</a>
            <a href="javascript:void(0);" id="filterManual" class="filter-link" style="margin: 0 5px;">Override (<?= number_format($filterCounts['manual_count']) ?>)</a>
            <a href="javascript:void(0);" id="filterDown" class="filter-link" style="margin: 0 5px;">Down (<?= number_format($filterCounts['down_count']) ?>)</a>
            <a href="javascript:void(0);" id="filterNotReviewed" class="filter-link" style="margin: 0 5px;">Not Reviewed (<?= number_format($filterCounts['not_reviewed_count']) ?>)</a>
            <a href="javascript:void(0);" id="filterChecked" class="filter-link" style="margin: 0 5px;">Checked (<?= number_format($filterCounts['checked_count']) ?>)</a>
            <a href="javascript:void(0);" id="filterNotChecked" class="filter-link" style="margin: 0 5px;">Not Checked (<?= number_format($filterCounts['not_checked_count']) ?>)</a>
            <span style="margin: 0 10px; color: #ccc;">|</span>
            <strong style="margin: 0 5px">Filter by date:</strong>
            <input type="date" id="datePicker" style="padding: 4px 8px; border: 1px solid #ddd; border-radius: 3px; margin: 0 5px;">
            <a href="javascript:void(0);" id="resetDateFilter" style="margin: 0 5px; color: #333; text-decoration: none;">Reset</a>
            <span style="margin: 0 10px; color: #ccc;">|</span>
            <div style="position: relative; display: flex; align-items: center;">
                <input type="text" id="searchBox" placeholder="Search products..." style="padding: 5px 25px 5px 8px; width: 200px; border: 1px solid #ddd; border-radius: 3px;">
                <span id="clearSearch" style="position: absolute; right: 1px; padding: 2px 8px; cursor: pointer; color: #333; background-color: #f0f0f0; font-size: 16px; display: none;">×</span>
            </div>
        </div>

        <!-- Results Info and Controls -->
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
            <div id="resultsInfo" style="font-size: 14px;"></div>
            <div style="display: flex; align-items: center; gap: 15px;">
                <div style="display: flex; align-items: center; gap: 5px;">
                    <select id="productsPerPage" style="padding: 4px 8px;">
                        <option value="10">10</option>
                        <option value="15">15</option>
                        <option value="25">25</option>
                        <option value="50">50</option>
                        <option value="100">100</option>
                    </select>
                    <span style="font-size: 14px;">products per page</span>
                </div>
            </div>
        </div>

        <!-- Products DataTable -->
        <table id="productsTable" class="display" style="width:100%">
            <thead>
                <tr>
                    <th>Product</th>
                    <th>Tagline</th>
                    <th>Description</th>
                    <th>Votes</th>
                    <th>Comments</th>
                    <th>Rating</th>
                    <th>Launch Date</th>
                    <th>Categories</th>
                    <th>Website</th>
                    <th>Checked</th>
                    <th>Created</th>
                    <th>Updated</th>
                </tr>
            </thead>
            <tbody>
        </table>
    </div>

    <!-- Manual URL Popup -->
    <div id="manualUrlPopup" class="popup-overlay">
        <div class="popup-content">
            <div class="popup-header">
                <h3>manual URL</h3>
                <span class="popup-close" onclick="closeManualUrlPopup()">&times;</span>
            </div>
            <form id="manualUrlForm" class="popup-form">
                <input type="hidden" name="csrf_token" value="<?= htmlspecialchars(Auth::generateCSRFToken(), ENT_QUOTES, 'UTF-8') ?>">
                <input type="hidden" name="product_id" id="popup_product_id">
                <input type="hidden" name="action" value="save">

                <label for="manual_url">Website URL:</label>
                <input type="url" name="manual_url" id="manual_url" placeholder="https://example.com" required>

                <div class="popup-buttons">
                    <button type="submit">Save</button>
                    <button type="button" onclick="closeManualUrlPopup()">Cancel</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Temporary Notice Container -->
    <div id="tempNotice" class="temp-notice"></div>

    <!-- jQuery and DataTables JS -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
    
    <script>
        // Available dates from PHP
        var availableDates = <?= json_encode($availableDates) ?>;
        var currentDateFilter = null;
        var currentStatusFilter = '';

        // Initialize filter state from URL immediately
        var urlParams = new URLSearchParams(window.location.search);
        var initialFilter = urlParams.get('filter');
        if (initialFilter) {
            currentStatusFilter = initialFilter;
            console.log('Setting currentStatusFilter to:', currentStatusFilter);
        }

        // Helper function to escape HTML
        function escapeHtml(text) {
            if (!text) return '';
            var map = {
                '&': '&amp;',
                '<': '&lt;',
                '>': '&gt;',
                '"': '&quot;',
                "'": '&#039;'
            };
            return text.toString().replace(/[&<>"']/g, function(m) { return map[m]; });
        }

        // Function to update filter counts
        function updateFilterCounts() {
            fetch('api/filter-counts.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const counts = data.counts;

                        // Update filter link texts with new counts (using comma separators)
                        document.getElementById('filterAll').textContent = `All Products (${counts.total.toLocaleString('en-US')})`;
                        document.getElementById('filterEmpty').textContent = `Empty (${counts.empty.toLocaleString('en-US')})`;
                        document.getElementById('filterManual').textContent = `Override (${counts.manual.toLocaleString('en-US')})`;
                        document.getElementById('filterDown').textContent = `Down (${counts.down.toLocaleString('en-US')})`;
                        document.getElementById('filterNotReviewed').textContent = `Not Reviewed (${counts.not_reviewed.toLocaleString('en-US')})`;
                        document.getElementById('filterChecked').textContent = `Checked (${counts.checked.toLocaleString('en-US')})`;
                        document.getElementById('filterNotChecked').textContent = `Not Checked (${counts.not_checked.toLocaleString('en-US')})`;
                    }
                })
                .catch(error => {
                    console.error('Error updating filter counts:', error);
                });
        }

        // Complex website column rendering function
        function renderWebsiteColumn(row) {
            var html = '';
            var effectiveStatus = row.status_override || row.external_url_status;

            if (row.manual_url) {
                // Manual URL override exists
                if (effectiveStatus === 'down') {
                    html += '<a href="' + escapeHtml(row.manual_url) + '" target="_blank" class="url-link">' + escapeHtml(row.manual_url) + '</a>';
                    html += '<div style="margin-top: 10px;">';
                    html += '<span style="font-size: 11px;">🔧</span>';
                    html += '<span style="margin-left: 3px;">URL overridden</span><br>';

                    if (row.status_override) {
                        html += '<span style="font-size: 11px; margin-left: 3px;">📌</span>';
                        html += '<span style="margin-left: 3px;">Status overridden to \'' + escapeHtml(row.status_override) + '\'</span><br>';
                        html += '<a href="javascript:void(0);" onclick="removeStatusOverride(\'' + escapeHtml(row.id) + '\')">remove status override</a>';
                    } else {
                        html += '<span>⚠️</span>';
                        html += '<span style="margin-left: 3px;">Down</span><br>';
                        html += '<a href="javascript:void(0);" onclick="markUrlStatus(\'' + escapeHtml(row.id) + '\', \'up\')">mark as up</a>';
                    }
                    html += '<a href="javascript:void(0);" onclick="removeManualUrl(\'' + escapeHtml(row.id) + '\')" style="margin-left: 5px;">remove URL override</a>';

                    if (row.reviewed_at) {
                        var reviewDate = new Date(row.reviewed_at).toLocaleDateString();
                        html += '<a href="javascript:void(0);" onclick="unmarkAsReviewed(\'' + escapeHtml(row.id) + '\')" style="margin-left: 5px;">remove review</a><br>';
                        html += '<span style="font-size: 11px;">🔍</span>';
                        html += '<span style="margin-left: 3px;">Reviewed ' + reviewDate + '</span><br>';
                    } else {
                        html += '<a href="javascript:void(0);" onclick="markAsReviewed(\'' + escapeHtml(row.id) + '\')" style="margin-left: 5px;">mark as reviewed</a>';
                    }
                } else {
                    html += '<a href="' + escapeHtml(row.manual_url) + '" target="_blank" class="url-link">' + escapeHtml(row.manual_url) + '</a>';
                    html += '<div style="margin-top: 10px;">';
                    html += '<span style="font-size: 11px;">🔧</span>';
                    html += '<span style="margin-left: 3px;">URL overridden</span><br>';

                    if (row.status_override) {
                        html += '<span style="font-size: 11px; margin-left: 3px;">📌</span>';
                        html += '<span style="margin-left: 3px;">Status overridden to \'' + escapeHtml(row.status_override) + '\'</span><br>';
                        html += '<a href="javascript:void(0);" onclick="removeStatusOverride(\'' + escapeHtml(row.id) + '\')">remove status override</a>';
                    } else {
                        html += '<a href="javascript:void(0);" onclick="markUrlStatus(\'' + escapeHtml(row.id) + '\', \'up\')">mark as up</a>';
                    }
                    html += '<a href="javascript:void(0);" onclick="removeManualUrl(\'' + escapeHtml(row.id) + '\')" style="margin-left: 5px">remove URL override</a>';
                }
                html += '</div>';

                if (row.external_url && row.external_url_status === 'up' && !row.status_override) {
                    html += '<div class="manual-url-notice">URL resolved automatically.</div>';
                }
            } else if (row.external_url && effectiveStatus === 'up') {
                html += '<a href="' + escapeHtml(row.external_url) + '" target="_blank" class="url-link">' + escapeHtml(row.external_url) + '</a>';
                if (row.status_override) {
                    html += '<div style="margin-top: 10px;">';
                    html += '<span style="font-size: 11px;">📌</span>';
                    html += '<span style="margin-left: 3px;">Status overridden to \'' + escapeHtml(row.status_override) + '\'</span><br>';
                    html += '<a href="javascript:void(0);" onclick="removeStatusOverride(\'' + escapeHtml(row.id) + '\')">remove status override</a>';
                    html += '</div>';
                }
            } else if (row.external_url && effectiveStatus === 'down') {
                html += '<a href="' + escapeHtml(row.external_url) + '" target="_blank" class="url-link">' + escapeHtml(row.external_url) + '</a>';
                html += '<div style="margin-top: 10px;">';

                if (row.status_override) {
                    html += '<span style="font-size: 11px;">📌</span>';
                    html += '<span style="margin-left: 3px;">Status overridden to \'' + escapeHtml(row.status_override) + '\'</span><br>';
                    html += '<a href="javascript:void(0);" onclick="removeStatusOverride(\'' + escapeHtml(row.id) + '\')">remove status override</a>';
                } else {
                    html += '<span style="font-size: 11px;">⚠️</span>';
                    html += '<span style="margin-left: 3px;">Down</span><br>';
                    html += '<a href="javascript:void(0);" onclick="markUrlStatus(\'' + escapeHtml(row.id) + '\', \'up\')">mark as up</a>';
                }
                html += '<a href="javascript:void(0);" onclick="openManualUrlPopup(\'' + escapeHtml(row.id) + '\')" style="margin-left: 5px;">manual URL</a>';

                if (row.reviewed_at) {
                    var reviewDate = new Date(row.reviewed_at).toLocaleDateString();
                    html += '<a href="javascript:void(0);" onclick="unmarkAsReviewed(\'' + escapeHtml(row.id) + '\')" style="margin-left: 5px;">remove review</a><br>';
                    html += '<span style="font-size: 11px;">🔍</span>';
                    html += '<span style="margin-left: 3px;">Reviewed ' + reviewDate + '</span><br>';
                } else {
                    html += '<a href="javascript:void(0);" onclick="markAsReviewed(\'' + escapeHtml(row.id) + '\')" style="margin-left: 5px;">mark as reviewed</a>';
                }
                html += '</div>';
            } else if (effectiveStatus === 'up' && row.status_override) {
                // Status overridden to 'up' but no external_url
                if (row.external_url) {
                    html += '<a href="' + escapeHtml(row.external_url) + '" target="_blank" class="url-link">' + escapeHtml(row.external_url) + '</a>';
                } else {
                    html += '<a href="' + escapeHtml(row.url) + '" target="_blank" class="url-link">original</a>';
                }
                html += '<div style="margin-top: 10px;">';
                html += '<span style="font-size: 11px;">📌</span>';
                html += '<span style="margin-left: 3px;">Status overridden to \'' + escapeHtml(row.status_override) + '\'</span><br>';
                html += '<a href="javascript:void(0);" onclick="removeStatusOverride(\'' + escapeHtml(row.id) + '\')">remove status override</a>';
                html += '</div>';
            } else if (effectiveStatus === 'down') {
                if (row.status_override) {
                    if (row.external_url) {
                        html += '<a href="' + escapeHtml(row.external_url) + '" target="_blank" class="url-link">' + escapeHtml(row.external_url) + '</a>';
                    } else {
                        html += '<a href="' + escapeHtml(row.url) + '" target="_blank" class="url-link">original</a>';
                    }
                    html += '<div style="margin-top: 10px;">';
                    html += '<span style="font-size: 11px;">📌</span>';
                    html += '<span style="margin-left: 3px;">Status overridden to \'' + escapeHtml(row.status_override) + '\'</span><br>';
                    html += '<a href="javascript:void(0);" onclick="removeStatusOverride(\'' + escapeHtml(row.id) + '\')">remove status override</a>';
                } else {
                    html += '<span style="font-size: 11px;">❌</span>';
                    html += '<span style="margin-left: 3px;">Down</span><br>';
                    html += '<a href="javascript:void(0);" onclick="markUrlStatus(\'' + escapeHtml(row.id) + '\', \'up\')">mark as up</a>';
                }
                html += '<a href="javascript:void(0);" onclick="openManualUrlPopup(\'' + escapeHtml(row.id) + '\')" style="margin-left: 5px;">manual URL</a>';

                if (row.reviewed_at) {
                    var reviewDate = new Date(row.reviewed_at).toLocaleDateString();
                    html += '<a href="javascript:void(0);" onclick="unmarkAsReviewed(\'' + escapeHtml(row.id) + '\')" style="margin-left: 5px;">remove review</a><br>';
                    html += '<span style="font-size: 11px;">🔍</span>';
                    html += '<span style="margin-left: 3px;">Reviewed ' + reviewDate + '</span><br>';
                } else {
                    html += '<a href="javascript:void(0);" onclick="markAsReviewed(\'' + escapeHtml(row.id) + '\')" style="margin-left: 5px;">mark as reviewed</a>';
                }
            } else {
                html += '<span style="font-size: 11px;">🕓</span>';
                html += '<span style="margin-left: 3px;">Empty</span><br>';
                html += '<a href="' + escapeHtml(row.url) + '" target="_blank" class="url-link">original</a>';
                html += '<a href="javascript:void(0);" onclick="openManualUrlPopup(\'' + escapeHtml(row.id) + '\')" style="margin-left: 5px;">manual URL</a>';
            }

            if (!row.product_url && !row.external_url && row.external_url_status !== 'down') {
                html += '<br>-';
            }

            return html;
        }

        // Global table variable
        var table;



        $(document).ready(function() {
            // Apply filter UI state if filter was set from URL
            if (initialFilter) {
                $('.filter-link').removeClass('active').css('font-weight', 'normal');

                // Map filter names to element IDs
                var filterMap = {
                    'Empty': '#filterEmpty',
                    'manual': '#filterManual',
                    'down': '#filterDown',
                    'not_reviewed': '#filterNotReviewed',
                    'checked': '#filterChecked',
                    'not_checked': '#filterNotChecked'
                };

                if (filterMap[initialFilter]) {
                    $(filterMap[initialFilter]).addClass('active').css('font-weight', 'bold');
                }
            }

            table = $('#productsTable').DataTable({
                "processing": true,
                "serverSide": true,
                "ajax": {
                    "url": "api/products.php",
                    "type": "POST",
                    "data": function(d) {
                        // Add custom filter parameters
                        d.statusFilter = currentStatusFilter;
                        d.dateFilter = currentDateFilter;
                    }
                },
                "pageLength": parseInt(getUrlParam('length')) || 10,
                "lengthMenu": [[10, 15, 25, 50, 100], [10, 15, 25, 50, 100]],
                "order": [[6, "desc"], [3, "desc"]], // Sort by Launch Date (column 6) desc, then Votes (column 3) desc
                "scrollX": true,
                "scrollCollapse": true,
                "dom": 't<"bottom"p>', // Only show table and pagination, hide default controls
                "info": false, // Hide default info display
                "columns": [
                    { "data": "name", "name": "product" },
                    { "data": "tagline", "name": "tagline" },
                    { "data": "description", "name": "description" },
                    { "data": "votes_count", "name": "votes" },
                    { "data": "comments_count", "name": "comments" },
                    { "data": "review_rating", "name": "rating" },
                    { "data": "date_created", "name": "launch_date" },
                    { "data": "categories", "name": "categories" },
                    { "data": "url", "name": "website" },
                    { "data": "url_checked", "name": "url_checked" },
                    { "data": "created_at", "name": "created" },
                    { "data": "updated_at", "name": "updated" }
                ],
                "columnDefs": [
                    {
                        "targets": [0], // Product name column
                        "render": function(data, type, row) {
                            if (type === 'display') {
                                if (row.product_url) {
                                    return '<a href="' + escapeHtml(row.product_url) + '" target="_blank" class="url-link">' + escapeHtml(data) + '</a>';
                                }
                                return escapeHtml(data);
                            }
                            return data;
                        }
                    },
                    {
                        "targets": [1], // Tagline column
                        "render": function(data, type, row) {
                            if (type === 'display') {
                                return '<div class="product-tagline">' + (data ? escapeHtml(data) : '-') + '</div>';
                            }
                            return data || '';
                        }
                    },
                    {
                        "targets": [2], // Description column
                        "render": function(data, type, row) {
                            if (type === 'display') {
                                return '<div class="product-description">' + (data ? escapeHtml(data) : '-') + '</div>';
                            }
                            return data || '';
                        }
                    },
                    {
                        "targets": [3, 4], // Votes and Comments columns
                        "render": function(data, type, row) {
                            if (type === 'display') {
                                return data ? parseInt(data).toLocaleString('en-US') : '0';
                            }
                            return data || 0;
                        }
                    },
                    {
                        "targets": [5], // Rating column
                        "render": function(data, type, row) {
                            if (type === 'display') {
                                return data && data > 0 ? parseFloat(data).toFixed(1) : '-';
                            }
                            return data;
                        }
                    },
                    {
                        "targets": [6], // Launch Date column
                        "render": function(data, type, row) {
                            if (type === 'display' && data) {
                                return new Date(data).toLocaleDateString('en-US', {
                                    year: 'numeric',
                                    month: 'short',
                                    day: 'numeric'
                                });
                            }
                            return data || '-';
                        }
                    },
                    {
                        "targets": [7], // Categories column
                        "render": function(data, type, row) {
                            if (type === 'display') {
                                return '<div class="categories-list">' + (data ? escapeHtml(data) : '-') + '</div>';
                            }
                            return data || '';
                        }
                    },
                    {
                        "targets": [8], // Website column - complex rendering
                        "render": function(data, type, row) {
                            if (type === 'display') {
                                return renderWebsiteColumn(row);
                            }
                            return data || '';
                        }
                    },
                    {
                        "targets": [9], // URL Checked column
                        "render": function(data, type, row) {
                            if (type === 'display') {
                                return data == 1 ? '<span>✓</span>' : '<span>-</span>';
                            }
                            return data;
                        }
                    },
                    {
                        "targets": [10, 11], // Created and Updated columns
                        "render": function(data, type, row) {
                            if (type === 'display' && data) {
                                return new Date(data).toLocaleDateString('en-US', {
                                    year: 'numeric',
                                    month: 'short',
                                    day: 'numeric',
                                    hour: '2-digit',
                                    minute: '2-digit',
                                    hour12: false
                                });
                            }
                            return data || '-';
                        }
                    }
                ],
                "language": {
                    "search": "Search products:",
                    "lengthMenu": "Show _MENU_ products per page",
                    "info": "Showing _START_ to _END_ of _TOTAL_ products",
                    "infoEmpty": "No products found",
                    "infoFiltered": "(filtered from _MAX_ total products)",
                    "processing": "Loading products..."
                },
                "drawCallback": function(settings) {
                    // Update filter counts after each draw/reload
                    updateFilterCounts();
                }
            });

            // Search functionality
            var searchTimeout;
            $('#searchBox').on('input', function() {
                var searchValue = $(this).val();

                // Show/hide clear button based on search content
                if (searchValue.length > 0) {
                    $('#clearSearch').show();
                } else {
                    $('#clearSearch').hide();
                }

                // Debounce search to avoid too many requests
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(function() {
                    table.search(searchValue).draw();
                    updateUrlParams({ 'search': searchValue || null, 'start': 0 });
                }, 300);
            });

            // Clear search functionality
            $('#clearSearch').click(function() {
                $('#searchBox').val('');
                $('#clearSearch').hide();
                table.search('').draw();
                updateUrlParams({ 'search': null, 'start': 0 });
            });

            // Initialize search state from URL
            var initialSearch = getUrlParam('search');
            if (initialSearch) {
                $('#searchBox').val(initialSearch);
                $('#clearSearch').show();
            }



            // Initialize date filter from URL
            var initialDate = getUrlParam('date');
            if (initialDate) {
                currentDateFilter = initialDate;
                $('#datePicker').val(initialDate);
            }

            // Helper function to clear all filters
            function clearAllFilters() {
                currentStatusFilter = '';
                table.ajax.reload();
            }

            // Restore state from URL parameters on page load
            function restoreStateFromUrl() {
                isRestoringState = true; // Prevent URL updates during restoration

                // Restore filter state first (this may reset pagination)
                var filterParam = getUrlParam('filter');
                if (filterParam === 'Empty') {
                    currentStatusFilter = 'Empty';
                    table.ajax.reload();
                    $('.filter-link').removeClass('active').css('font-weight', 'normal');
                    $('#filterEmpty').addClass('active').css('font-weight', 'bold');
                } else if (filterParam === 'manual') {
                    currentStatusFilter = 'manual';
                    table.ajax.reload();
                    $('.filter-link').removeClass('active').css('font-weight', 'normal');
                    $('#filterManual').addClass('active').css('font-weight', 'bold');
                } else if (filterParam === 'checked') {
                    currentStatusFilter = 'checked';
                    table.ajax.reload();
                    $('.filter-link').removeClass('active').css('font-weight', 'normal');
                    $('#filterChecked').addClass('active').css('font-weight', 'bold');
                } else if (filterParam === 'not_checked') {
                    currentStatusFilter = 'not_checked';
                    table.ajax.reload();
                    $('.filter-link').removeClass('active').css('font-weight', 'normal');
                    $('#filterNotChecked').addClass('active').css('font-weight', 'bold');
                } else if (filterParam === 'down') {
                    currentStatusFilter = 'down';
                    table.ajax.reload();
                    $('.filter-link').removeClass('active').css('font-weight', 'normal');
                    $('#filterDown').addClass('active').css('font-weight', 'bold');
                } else if (filterParam === 'not_reviewed') {
                    currentStatusFilter = 'not_reviewed';
                    table.ajax.reload();
                    $('.filter-link').removeClass('active').css('font-weight', 'normal');
                    $('#filterNotReviewed').addClass('active').css('font-weight', 'bold');
                } else {
                    // Clear search and update UI
                    table.column(7).search('').draw();
                    $('.filter-link').removeClass('active').css('font-weight', 'normal');
                    $('#filterAll').addClass('active').css('font-weight', 'bold');
                }

                // Restore date filter
                var dateParam = getUrlParam('date');
                if (dateParam && availableDates.includes(dateParam)) {
                    $('#datePicker').val(dateParam).trigger('change');
                }

                // Restore pagination AFTER filter is applied
                var startParam = parseInt(getUrlParam('start')) || 0;
                if (startParam > 0) {
                    var pageLength = table.page.len();
                    var targetPage = Math.floor(startParam / pageLength);
                    table.page(targetPage).draw(false); // false = don't reset paging
                }

                // Re-enable URL updates after restoration is complete
                setTimeout(function() {
                    isRestoringState = false;
                }, 100); // Balanced timeout for proper state restoration
            }

            // Set up custom controls
            setupCustomControls();

            // Call restore function after table is fully initialized
            setTimeout(function() {
                restoreStateFromUrl();
                handleNotificationFromUrl();
                updateResultsInfo(); // Update results info after table is ready
            }, 300); // Quick delay for proper filter then pagination restoration

            // Handle notification from URL parameters
            function handleNotificationFromUrl() {
                var notificationParam = getUrlParam('notification');
                var notificationTypeParam = getUrlParam('notification_type') || 'success';

                if (notificationParam) {
                    // Show the notification
                    showTempNotice(decodeURIComponent(notificationParam), notificationTypeParam);

                    // Remove notification parameters from URL after showing
                    setTimeout(function() {
                        removeNotificationFromUrl();
                    }, 4500); // Remove after notification disappears
                }
            }

            // Remove notification parameters from URL
            function removeNotificationFromUrl() {
                var url = new URL(window.location);
                url.searchParams.delete('notification');
                url.searchParams.delete('notification_type');

                // Update URL without page refresh
                window.history.replaceState({}, document.title, url.toString());
            }

            // Add event listeners to preserve URL state
            table.on('page.dt', function() {
                var info = table.page.info();
                updateUrlParams({
                    'start': info.start,
                    'length': info.length
                });
                updateResultsInfo();
            });

            table.on('length.dt', function(e, settings, len) {
                updateUrlParams({
                    'length': len,
                    'start': 0 // Reset to first page when changing page length
                });
                updateResultsInfo();
            });

            table.on('draw.dt', function() {
                updateResultsInfo();
            });

            // Setup custom controls
            function setupCustomControls() {
                // Set initial values from URL params
                var lengthParam = getUrlParam('length') || '10';
                var searchParam = getUrlParam('search') || '';

                $('#productsPerPage').val(lengthParam);
                $('#searchBox').val(searchParam);

                // Handle page length change
                $('#productsPerPage').on('change', function() {
                    var newLength = parseInt($(this).val());
                    table.page.len(newLength).draw();
                });

                // Handle search
                $('#searchBox').on('keyup search', function() {
                    var searchValue = $(this).val();
                    table.search(searchValue).draw();
                    updateUrlParams({ 'search': searchValue || null, 'start': 0 });
                });
            }

            // Update results info
            function updateResultsInfo() {
                var info = table.page.info();
                var resultsText = '';

                if (info.recordsDisplay === 0) {
                    resultsText = 'No products found';
                } else if (info.recordsTotal === info.recordsDisplay) {
                    resultsText = 'Showing ' + (info.start + 1) + ' to ' + info.end + ' of ' + info.recordsDisplay + ' products';
                } else {
                    resultsText = 'Showing ' + (info.start + 1) + ' to ' + info.end + ' of ' + info.recordsDisplay + ' products (filtered from ' + info.recordsTotal + ' total products)';
                }

                $('#resultsInfo').text(resultsText);
            }

            table.on('search.dt', function() {
                var searchValue = table.search();
                updateUrlParams({
                    'search': searchValue,
                    'start': 0 // Reset to first page when searching
                });
            });

            // Update live time every second (both local and Pacific time)
            function updateLiveTime() {
                const now = new Date();

                // Local time
                const localTime = now.toLocaleString('en-US', {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit',
                    hour12: false
                });

                // Pacific time (Product Hunt timezone)
                const pacificTime = now.toLocaleString('en-US', {
                    timeZone: 'America/Los_Angeles',
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit',
                    hour12: false
                });

                document.getElementById('localTime').textContent = 'Local: ' + localTime;
                document.getElementById('pacificTime').textContent = 'Pacific: ' + pacificTime;
            }

            // Update immediately and then every second
            updateLiveTime();
            setInterval(updateLiveTime, 1000);

            // Fix DataTables header on window resize
            $(window).on('resize', function() {
                $('#productsTable').DataTable().columns.adjust().draw();
            });

            // Filter functionality
            $('#filterAll').click(function(e) {
                e.preventDefault();
                clearAllFilters();
                table.draw();
                $('.filter-link').removeClass('active').css('font-weight', 'normal');
                $(this).addClass('active').css('font-weight', 'bold');
                updateUrlParams({ 'filter': null });
            });

            $('#filterEmpty').click(function(e) {
                e.preventDefault();
                currentStatusFilter = 'Empty';
                table.ajax.reload();
                $('.filter-link').removeClass('active').css('font-weight', 'normal');
                $(this).addClass('active').css('font-weight', 'bold');
                updateUrlParams({ 'filter': 'Empty' });
            });

            $('#filterChecked').click(function(e) {
                e.preventDefault();
                currentStatusFilter = 'checked';
                table.ajax.reload();
                $('.filter-link').removeClass('active').css('font-weight', 'normal');
                $(this).addClass('active').css('font-weight', 'bold');
                updateUrlParams({ 'filter': 'checked' });
            });

            $('#filterNotChecked').click(function(e) {
                e.preventDefault();
                currentStatusFilter = 'not_checked';
                table.ajax.reload();
                $('.filter-link').removeClass('active').css('font-weight', 'normal');
                $(this).addClass('active').css('font-weight', 'bold');
                updateUrlParams({ 'filter': 'not_checked' });
            });

            $('#filterManual').click(function(e) {
                e.preventDefault();
                currentStatusFilter = 'manual';
                table.ajax.reload();
                $('.filter-link').removeClass('active').css('font-weight', 'normal');
                $(this).addClass('active').css('font-weight', 'bold');
                updateUrlParams({ 'filter': 'manual' });
            });

            $('#filterDown').click(function(e) {
                e.preventDefault();
                currentStatusFilter = 'down';
                table.ajax.reload();
                $('.filter-link').removeClass('active').css('font-weight', 'normal');
                $(this).addClass('active').css('font-weight', 'bold');
                updateUrlParams({ 'filter': 'down' });
            });

            $('#filterNotReviewed').click(function(e) {
                e.preventDefault();
                currentStatusFilter = 'not_reviewed';
                table.ajax.reload();
                $('.filter-link').removeClass('active').css('font-weight', 'normal');
                $(this).addClass('active').css('font-weight', 'bold');
                updateUrlParams({ 'filter': 'not_reviewed' });
            });

            // Set up date picker with available dates
            var datePicker = document.getElementById('datePicker');
            datePicker.min = availableDates[availableDates.length - 1]; // Oldest date
            datePicker.max = availableDates[0]; // Newest date

            // Date filter functionality
            $('#datePicker').change(function() {
                var selectedDate = $(this).val();

                if (selectedDate === '') {
                    // Clear date filter
                    currentDateFilter = null;
                    table.ajax.reload();
                    updateUrlParams({ 'date': null });
                } else if (availableDates.includes(selectedDate)) {
                    currentDateFilter = selectedDate;
                    table.ajax.reload();
                    updateUrlParams({ 'date': selectedDate });
                } else {
                    // Invalid date selected, clear it
                    $(this).val('');
                    currentDateFilter = null;
                    table.column(5).search('').draw();
                    updateUrlParams({ 'date': null, 'start': 0 });
                    alert('Please select a valid date with available data.');
                }
            });

            // Reset date filter
            $('#resetDateFilter').click(function(e) {
                e.preventDefault();
                $('#datePicker').val('');
                currentDateFilter = null;
                table.ajax.reload();
                updateUrlParams({ 'date': null, 'start': 0 });
            });

            // Date filter is now handled server-side, no need for client-side reapplication
        });

        // Temporary notice functions
        function showTempNotice(message, type = 'success', duration = 4000) {
            const notice = document.getElementById('tempNotice');
            notice.textContent = message;
            notice.className = 'temp-notice ' + type;

            // Show notice
            setTimeout(() => notice.classList.add('show'), 100);

            // Hide notice after duration
            setTimeout(() => {
                notice.classList.remove('show');
                setTimeout(() => {
                    notice.textContent = '';
                    notice.className = 'temp-notice';
                }, 300);
            }, duration);
        }

        // URL state management functions
        var isRestoringState = false; // Flag to prevent URL updates during state restoration

        function updateUrlParams(params) {
            if (isRestoringState) return; // Don't update URL during state restoration

            const url = new URL(window.location);
            Object.keys(params).forEach(key => {
                if (params[key] !== null && params[key] !== '') {
                    url.searchParams.set(key, params[key]);
                } else {
                    url.searchParams.delete(key);
                }
            });
            window.history.replaceState({}, '', url);
        }

        function getUrlParam(param) {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(param);
        }

        function getCurrentUrlWithParams() {
            return window.location.href;
        }

        // Manual URL popup functions
        function openManualUrlPopup(productId) {
            document.getElementById('popup_product_id').value = productId;
            document.getElementById('manual_url').value = '';
            document.getElementById('manualUrlPopup').style.display = 'block';
            document.getElementById('manual_url').focus();
        }

        function closeManualUrlPopup() {
            document.getElementById('manualUrlPopup').style.display = 'none';
        }

        // Mark URL status function
        function markUrlStatus(productId, status) {
            fetch('api/mark_url_status.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    product_id: parseInt(productId),
                    status: status
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Refresh the DataTable to show updated data
                    table.ajax.reload(null, false); // false = stay on current page
                    showTempNotice(`URL status updated to '${status}' successfully!`, 'success');
                } else {
                    showTempNotice('Error: ' + (data.error || 'Unknown error occurred'), 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                // Show error notification via URL parameter
                var currentUrl = getCurrentUrlWithParams();
                var separator = currentUrl.includes('?') ? '&' : '?';
                window.location.href = currentUrl + separator + 'notification=' + encodeURIComponent('Network error occurred. Please try again.') + '&notification_type=error';
            });
        }

        // Close popup on Escape key and handle pagination shortcuts
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeManualUrlPopup();
            }

            // Pagination keyboard shortcuts (only when not typing in input fields)
            if (!e.target.matches('input, textarea, select')) {
                if (e.key === 'ArrowLeft') {
                    e.preventDefault();
                    // Go to previous page
                    var currentPage = table.page();
                    if (currentPage > 0) {
                        table.page(currentPage - 1).draw('page');
                    }
                } else if (e.key === 'ArrowRight') {
                    e.preventDefault();
                    // Go to next page
                    var info = table.page.info();
                    if (info.page < info.pages - 1) {
                        table.page(info.page + 1).draw('page');
                    }
                }
            }
        });

        // Handle manual URL form submission
        document.getElementById('manualUrlForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            formData.append('type', 'url'); // Add type parameter for URL overrides

            fetch('api/manual_url_api.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    closeManualUrlPopup();
                    showTempNotice('Manual URL saved successfully!', 'success');
                    table.ajax.reload(null, false); // Reload table data without resetting pagination
                } else {
                    showTempNotice('Error: ' + data.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showTempNotice('An error occurred while saving the URL.', 'error');
            });
        });

        // Remove manual URL override
        function removeManualUrl(productId) {
            const formData = new FormData();
            formData.append('action', 'remove');
            formData.append('product_id', productId);
            formData.append('type', 'url');
            formData.append('csrf_token', '<?= htmlspecialchars(Auth::generateCSRFToken(), ENT_QUOTES, 'UTF-8') ?>');

            fetch('api/manual_url_api.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Refresh the DataTable to show updated data
                    table.ajax.reload(null, false); // false = stay on current page
                    showTempNotice('Manual URL override removed successfully!', 'success');
                } else {
                    showTempNotice('Error: ' + (data.message || 'Unknown error occurred'), 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showTempNotice('An error occurred while removing the override.', 'error');
            });
        }

        // Remove status override
        function removeStatusOverride(productId) {
            const formData = new FormData();
            formData.append('action', 'remove');
            formData.append('product_id', productId);
            formData.append('type', 'status');
            formData.append('csrf_token', '<?= htmlspecialchars(Auth::generateCSRFToken(), ENT_QUOTES, 'UTF-8') ?>');

            fetch('api/manual_url_api.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Refresh the DataTable to show updated data
                    table.ajax.reload(null, false); // false = stay on current page
                    showTempNotice('Status override removed successfully!', 'success');
                } else {
                    showTempNotice('Error: ' + (data.message || 'Unknown error occurred'), 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showTempNotice('An error occurred while removing the status override.', 'error');
            });
        }

        // Mark product as reviewed
        function markAsReviewed(productId) {
            fetch('api/reviews.php?action=mark&product_id=' + encodeURIComponent(productId), {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showTempNotice('Product marked as reviewed successfully!', 'success');
                    table.ajax.reload(null, false); // Reload table data without resetting pagination
                } else {
                    showTempNotice('Error: ' + (data.error || 'Unknown error occurred'), 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showTempNotice('Network error occurred. Please try again.', 'error');
            });
        }

        // Remove review status
        function unmarkAsReviewed(productId) {
            fetch('api/reviews.php?action=unmark&product_id=' + encodeURIComponent(productId), {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showTempNotice('Review status removed successfully!', 'success');
                    table.ajax.reload(null, false); // Reload table data without resetting pagination
                } else {
                    showTempNotice('Error: ' + (data.error || 'Unknown error occurred'), 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showTempNotice('Network error occurred. Please try again.', 'error');
            });
        }

    </script>
</body>
</html>
