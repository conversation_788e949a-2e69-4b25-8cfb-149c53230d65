<?php
/**
 * Secure Authentication System
 * Handles login, logout, and session management
 */

require_once __DIR__ . '/../config/config.php';

class Auth {
    private static $sessionName = 'product_hunt_auth';
    private static $sessionTimeout = 3600; // 1 hour
    
    /**
     * Start secure session
     */
    public static function startSession() {
        if (session_status() === PHP_SESSION_NONE) {
            // Secure session configuration
            ini_set('session.cookie_httponly', 1);
            ini_set('session.cookie_secure', isset($_SERVER['HTTPS']) ? 1 : 0);
            ini_set('session.use_strict_mode', 1);
            ini_set('session.cookie_samesite', 'Strict');
            
            session_start();
            
            // Regenerate session ID periodically for security
            if (!isset($_SESSION['last_regeneration'])) {
                $_SESSION['last_regeneration'] = time();
            } elseif (time() - $_SESSION['last_regeneration'] > 300) { // 5 minutes
                session_regenerate_id(true);
                $_SESSION['last_regeneration'] = time();
            }
        }
    }
    
    /**
     * Check if user is authenticated
     */
    public static function isAuthenticated() {
        self::startSession();
        
        if (!isset($_SESSION[self::$sessionName])) {
            return false;
        }
        
        // Check session timeout
        if (isset($_SESSION['last_activity']) && 
            (time() - $_SESSION['last_activity'] > self::$sessionTimeout)) {
            self::logout();
            return false;
        }
        
        // Update last activity time
        $_SESSION['last_activity'] = time();
        
        return $_SESSION[self::$sessionName] === true;
    }
    
    /**
     * Authenticate user with credentials
     */
    public static function authenticate($username, $password) {
        // Rate limiting - simple implementation
        self::startSession();
        
        if (!isset($_SESSION['login_attempts'])) {
            $_SESSION['login_attempts'] = 0;
            $_SESSION['last_attempt'] = 0;
        }
        
        // Block if too many attempts (5 attempts in 15 minutes)
        if ($_SESSION['login_attempts'] >= 5 && 
            (time() - $_SESSION['last_attempt']) < 900) {
            return [
                'success' => false,
                'message' => 'Too many login attempts. Please try again in 15 minutes.'
            ];
        }
        
        // Reset attempts if enough time has passed
        if ((time() - $_SESSION['last_attempt']) > 900) {
            $_SESSION['login_attempts'] = 0;
        }
        
        // Validate credentials
        $validUsername = defined('WEB_USER') ? WEB_USER : '';
        $validPassword = defined('WEB_PASSWORD') ? WEB_PASSWORD : '';
        
        if (empty($validUsername) || empty($validPassword)) {
            return [
                'success' => false,
                'message' => 'Authentication not configured properly.'
            ];
        }
        
        // Use timing-safe comparison to prevent timing attacks
        $usernameValid = hash_equals($validUsername, $username);
        $passwordValid = hash_equals($validPassword, $password);
        
        if ($usernameValid && $passwordValid) {
            // Successful login
            $_SESSION[self::$sessionName] = true;
            $_SESSION['last_activity'] = time();
            $_SESSION['login_attempts'] = 0; // Reset attempts
            $_SESSION['username'] = $username;
            
            // Regenerate session ID for security
            session_regenerate_id(true);
            
            return [
                'success' => true,
                'message' => 'Login successful.'
            ];
        } else {
            // Failed login
            $_SESSION['login_attempts']++;
            $_SESSION['last_attempt'] = time();
            
            return [
                'success' => false,
                'message' => 'Invalid username or password.'
            ];
        }
    }
    
    /**
     * Logout user
     */
    public static function logout() {
        self::startSession();
        
        // Clear all session data
        $_SESSION = array();
        
        // Delete session cookie
        if (isset($_COOKIE[session_name()])) {
            setcookie(session_name(), '', time() - 3600, '/');
        }
        
        // Destroy session
        session_destroy();
    }
    
    /**
     * Require authentication - redirect to login if not authenticated
     */
    public static function requireAuth() {
        if (!self::isAuthenticated()) {
            // Store the requested URL for redirect after login
            self::startSession();
            $_SESSION['redirect_after_login'] = $_SERVER['REQUEST_URI'];
            
            header('Location: login.php');
            exit;
        }
    }
    
    /**
     * Get current username
     */
    public static function getUsername() {
        self::startSession();
        return isset($_SESSION['username']) ? $_SESSION['username'] : null;
    }
    
    /**
     * Generate CSRF token
     */
    public static function generateCSRFToken() {
        self::startSession();
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        return $_SESSION['csrf_token'];
    }
    
    /**
     * Verify CSRF token
     */
    public static function verifyCSRFToken($token) {
        self::startSession();
        return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
    }
}

/**
 * Helper function to escape HTML output
 */
function escape($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}
?>
