<?php
/**
 * Simple and reliable Slack notification class
 * Uses Slack Incoming Webhooks - the most reliable method
 */

class SlackNotifier {
    private $webhookUrl;
    private $enabled;
    
    public function __construct() {
        $this->webhookUrl = $_ENV['SLACK_WEBHOOK_URL'] ?? '';
        $this->enabled = filter_var($_ENV['SLACK_NOTIFICATIONS_ENABLED'] ?? false, FILTER_VALIDATE_BOOLEAN);
    }
    
    /**
     * Send a notification to Slack
     * 
     * @param string $message The message to send
     * @param string $level The severity level (info, warning, error, critical)
     * @param array $context Additional context data
     * @return bool Success status
     */
    public function notify($message, $level = 'info', $context = []) {
        if (!$this->enabled) {
            return true; // Silently succeed if disabled
        }
        
        if (empty($this->webhookUrl)) {
            error_log("Slack webhook URL not configured");
            return false;
        }
        
        $payload = $this->buildPayload($message, $level, $context);
        
        return $this->sendToSlack($payload);
    }
    
    /**
     * Send critical error notification
     */
    public function critical($message, $context = []) {
        return $this->notify($message, 'critical', $context);
    }
    
    /**
     * Send error notification
     */
    public function error($message, $context = []) {
        return $this->notify($message, 'error', $context);
    }
    
    /**
     * Send warning notification
     */
    public function warning($message, $context = []) {
        return $this->notify($message, 'warning', $context);
    }
    
    /**
     * Send info notification
     */
    public function info($message, $context = []) {
        return $this->notify($message, 'info', $context);
    }
    
    /**
     * Build Slack payload
     */
    private function buildPayload($message, $level, $context) {
        $emoji = $this->getLevelEmoji($level);
        $color = $this->getLevelColor($level);
        
        $hostname = gethostname() ?: 'unknown';
        $timestamp = date('Y-m-d H:i:s T');
        
        $payload = [
            'username' => 'Product Hunt Monitor',
            'icon_emoji' => ':robot_face:',
            'attachments' => [
                [
                    'color' => $color,
                    'title' => $emoji . ' ' . ucfirst($level) . ' Alert',
                    'text' => $message,
                    'fields' => [
                        [
                            'title' => 'Server',
                            'value' => $hostname,
                            'short' => true
                        ],
                        [
                            'title' => 'Time',
                            'value' => $timestamp,
                            'short' => true
                        ]
                    ],
                    'footer' => 'Product Hunt System',
                    'ts' => time()
                ]
            ]
        ];
        
        // Add context fields if provided
        if (!empty($context)) {
            foreach ($context as $key => $value) {
                $payload['attachments'][0]['fields'][] = [
                    'title' => ucfirst(str_replace('_', ' ', $key)),
                    'value' => is_array($value) ? json_encode($value) : (string)$value,
                    'short' => strlen((string)$value) < 50
                ];
            }
        }
        
        return $payload;
    }
    
    /**
     * Get emoji for level
     */
    private function getLevelEmoji($level) {
        $emojis = [
            'info' => ':information_source:',
            'warning' => ':warning:',
            'error' => ':x:',
            'critical' => ':rotating_light:'
        ];
        
        return $emojis[$level] ?? ':grey_question:';
    }
    
    /**
     * Get color for level
     */
    private function getLevelColor($level) {
        $colors = [
            'info' => '#36a64f',      // Green
            'warning' => '#ff9500',   // Orange
            'error' => '#ff0000',     // Red
            'critical' => '#8B0000'   // Dark Red
        ];
        
        return $colors[$level] ?? '#cccccc';
    }
    
    /**
     * Send payload to Slack using cURL
     */
    private function sendToSlack($payload) {
        $ch = curl_init();
        
        curl_setopt_array($ch, [
            CURLOPT_URL => $this->webhookUrl,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($payload),
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/json',
                'User-Agent: ProductHunt-Monitor/1.0'
            ],
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 10,
            CURLOPT_CONNECTTIMEOUT => 5,
            CURLOPT_SSL_VERIFYPEER => true,
            CURLOPT_SSL_VERIFYHOST => 2,
            CURLOPT_FOLLOWLOCATION => false
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        
        curl_close($ch);
        
        if ($response === false || !empty($error)) {
            error_log("Slack notification failed - cURL error: " . $error);
            return false;
        }
        
        if ($httpCode !== 200) {
            error_log("Slack notification failed - HTTP $httpCode: " . $response);
            return false;
        }
        
        return true;
    }
    
    /**
     * Test the Slack connection
     */
    public function test() {
        return $this->info("🧪 Slack notification test - system is working correctly!");
    }
}
?>
