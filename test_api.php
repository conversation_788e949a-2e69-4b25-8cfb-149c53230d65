<?php
/**
 * Quick API diagnostic script
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>API Diagnostic Test</h2>";

try {
    // Test database connection
    require_once 'config/config.php';
    
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
        DB_USER,
        DB_PASSWORD,
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    
    echo "✅ Database connection: OK<br>";
    
    // Test basic count
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM products");
    $count = $stmt->fetchColumn();
    echo "✅ Total products: " . number_format($count) . "<br>";
    
    // Test basic select
    $stmt = $pdo->query("SELECT id, name, date_created, votes_count FROM products ORDER BY date_created DESC LIMIT 5");
    $products = $stmt->fetchAll();
    echo "✅ Basic query: OK (" . count($products) . " results)<br>";
    
    // Test the ACTUAL API queries to find the real bottleneck

    // Test 1: Total count query (from API)
    $start_time = microtime(true);
    $stmt = $pdo->query("
        SELECT COUNT(DISTINCT p.id)
        FROM products p
        LEFT JOIN product_categories pc ON p.id = pc.product_id
        LEFT JOIN categories c ON pc.category_id = c.id
        LEFT JOIN overrides url_override ON p.id = url_override.product_id AND url_override.type = 'url'
        LEFT JOIN overrides status_override ON p.id = status_override.product_id AND status_override.type = 'status'
        LEFT JOIN product_reviews pr ON p.id = pr.product_id
    ");
    $totalCount = $stmt->fetchColumn();
    $end_time = microtime(true);
    $time1 = round(($end_time - $start_time) * 1000, 2);
    echo "Test 1 - Total count (API query): {$time1}ms<br>";

    // Test 2: Main product query (from API)
    $start_time = microtime(true);
    $stmt = $pdo->query("
        SELECT
            p.*,
            url_override.rule as manual_url,
            url_override.created_at as manual_url_created_at,
            status_override.rule as status_override,
            MAX(pr.reviewed_at) as reviewed_at
        FROM products p
        LEFT JOIN product_categories pc ON p.id = pc.product_id
        LEFT JOIN categories c ON pc.category_id = c.id
        LEFT JOIN overrides url_override ON p.id = url_override.product_id AND url_override.type = 'url'
        LEFT JOIN overrides status_override ON p.id = status_override.product_id AND status_override.type = 'status'
        LEFT JOIN product_reviews pr ON p.id = pr.product_id
        GROUP BY p.id, url_override.rule, url_override.created_at, status_override.rule
        ORDER BY p.date_created DESC, p.votes_count DESC
        LIMIT 25
    ");
    $products = $stmt->fetchAll();
    $end_time = microtime(true);
    $time2 = round(($end_time - $start_time) * 1000, 2);
    echo "Test 2 - Main product query: {$time2}ms (" . count($products) . " products)<br>";

    // Test 3: Category query for those products
    if (!empty($products)) {
        $productIds = array_column($products, 'id');
        $placeholders = str_repeat('?,', count($productIds) - 1) . '?';

        $start_time = microtime(true);
        $stmt = $pdo->prepare("
            SELECT
                pc.product_id,
                GROUP_CONCAT(DISTINCT c.name ORDER BY c.name SEPARATOR ', ') as categories
            FROM product_categories pc
            JOIN categories c ON pc.category_id = c.id
            WHERE pc.product_id IN ($placeholders)
            GROUP BY pc.product_id
        ");
        $stmt->execute($productIds);
        $categories = $stmt->fetchAll();
        $end_time = microtime(true);
        $time3 = round(($end_time - $start_time) * 1000, 2);
        echo "Test 3 - Category query: {$time3}ms (" . count($categories) . " category groups)<br>";
    }

    // Test 4: Check if indexes exist
    $stmt = $pdo->query("SHOW INDEX FROM overrides");
    $overrideIndexes = $stmt->fetchAll();
    echo "Test 4 - Override table indexes: " . count($overrideIndexes) . "<br>";

    $stmt = $pdo->query("SHOW INDEX FROM product_categories");
    $pcIndexes = $stmt->fetchAll();
    echo "Test 5 - Product_categories indexes: " . count($pcIndexes) . "<br>";

    // Total time simulation
    $totalTime = $time1 + $time2 + ($time3 ?? 0);
    echo "<br><strong>Performance Analysis:</strong><br>";
    echo "🕒 Estimated total API time: {$totalTime}ms<br>";

    if ($time1 > 2000) echo "❌ COUNT query is very slow - complex JOINs issue<br>";
    if ($time2 > 3000) echo "❌ Main query is very slow - missing indexes<br>";
    if (($time3 ?? 0) > 1000) echo "❌ Category query is slow - foreign key indexes needed<br>";

    if ($totalTime > 8000) echo "🚨 Total time > 8 seconds - major optimization needed<br>";
    elseif ($totalTime > 3000) echo "⚠️ Total time > 3 seconds - optimization recommended<br>";
    else echo "✅ Query performance should be acceptable<br>";
    
    // Test indexes
    $stmt = $pdo->query("SHOW INDEX FROM products");
    $indexes = $stmt->fetchAll();
    echo "✅ Total indexes: " . count($indexes) . "<br>";
    
    // Check for FULLTEXT index
    $fulltext_found = false;
    foreach ($indexes as $index) {
        if ($index['Key_name'] === 'idx_description_fulltext') {
            $fulltext_found = true;
            break;
        }
    }
    
    if ($fulltext_found) {
        echo "⚠️  FULLTEXT index found - testing...<br>";
        try {
            $stmt = $pdo->query("SELECT COUNT(*) FROM products WHERE MATCH(description) AGAINST('app' IN NATURAL LANGUAGE MODE)");
            $fulltext_count = $stmt->fetchColumn();
            echo "✅ FULLTEXT index: OK (" . $fulltext_count . " matches)<br>";
        } catch (Exception $e) {
            echo "❌ FULLTEXT index error: " . $e->getMessage() . "<br>";
        }
    }
    
    echo "<br><h3>Sample Results:</h3>";
    echo "<table border='1'>";
    echo "<tr><th>ID</th><th>Name</th><th>Date</th><th>Votes</th><th>Categories</th></tr>";
    foreach (array_slice($results, 0, 5) as $row) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($row['id']) . "</td>";
        echo "<td>" . htmlspecialchars($row['name']) . "</td>";
        echo "<td>" . htmlspecialchars($row['date_created']) . "</td>";
        echo "<td>" . htmlspecialchars($row['votes_count']) . "</td>";
        echo "<td>" . htmlspecialchars($row['categories'] ?? '') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
    echo "❌ File: " . $e->getFile() . " Line: " . $e->getLine() . "<br>";
}
?>
