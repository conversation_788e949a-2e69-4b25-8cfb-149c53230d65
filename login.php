<?php
/**
 * Login Page
 * Secure authentication for Product Hunt dashboard
 */

require_once 'includes/auth.php';

// If already authenticated, redirect to dashboard
if (Auth::isAuthenticated()) {
    $redirectUrl = isset($_SESSION['redirect_after_login']) ? $_SESSION['redirect_after_login'] : 'index.php';
    unset($_SESSION['redirect_after_login']);
    header('Location: ' . $redirectUrl);
    exit;
}

$error = '';
$success = '';

// Handle URL parameters for messages
if (isset($_GET['message'])) {
    switch ($_GET['message']) {
        case 'logged_out':
            $success = 'You have been successfully logged out.';
            break;
    }
}

if (isset($_GET['error'])) {
    switch ($_GET['error']) {
        case 'invalid_token':
            $error = 'Invalid security token, but you have been logged out for security.';
            break;
    }
}

// Handle login form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';
    $csrfToken = $_POST['csrf_token'] ?? '';
    
    // Verify CSRF token
    if (!Auth::verifyCSRFToken($csrfToken)) {
        $error = 'Invalid request. Please try again.';
    } else {
        $result = Auth::authenticate($username, $password);
        
        if ($result['success']) {
            $success = $result['message'];
            
            // Redirect after successful login
            $redirectUrl = isset($_SESSION['redirect_after_login']) ? $_SESSION['redirect_after_login'] : 'index.php';
            unset($_SESSION['redirect_after_login']);
            
            header('Location: ' . $redirectUrl);
            exit;
        } else {
            $error = $result['message'];
        }
    }
}

$csrfToken = Auth::generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" type="image/png" href="https://<?php echo $_SERVER['HTTP_HOST']; ?>/assets/img/favicon.png">
    <title>Login - Product Hunt Dashboard</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            font-size: 13px;
        }

        .login-container {
            max-width: 400px;
            margin: 50px auto;
        }

        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
        }

        .form-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ccc;
            font-size: 13px;
            box-sizing: border-box;
        }

        .login-button {
            width: 100%;
            padding: 8px;
            border: 1px solid #ccc;
            background: #f0f0f0;
            cursor: pointer;
            font-size: 13px;
        }

        .alert {
            padding: 10px;
            margin-bottom: 15px;
            border: 1px solid #ccc;
            font-size: 13px;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1><a href="<?php echo $_SERVER['HTTP_HOST']; ?>" style="text-decoration: none; color: inherit;">Product Hunt Dashboard</a></h1>
        </div>
        
        <?php if ($error): ?>
            <div class="alert alert-error">
                <?= escape($error) ?>
            </div>
        <?php endif; ?>
        
        <?php if ($success): ?>
            <div class="alert alert-success">
                <?= escape($success) ?>
            </div>
        <?php endif; ?>
        
        <form method="POST" action="">
            <input type="hidden" name="csrf_token" value="<?= escape($csrfToken) ?>">
            
            <div class="form-group">
                <label for="username">Username</label>
                <input type="text" id="username" name="username" required autocomplete="username" 
                       value="<?= escape($_POST['username'] ?? '') ?>">
            </div>
            
            <div class="form-group">
                <label for="password">Password</label>
                <input type="password" id="password" name="password" required autocomplete="current-password">
            </div>
            
            <button type="submit" class="login-button">
                Sign In
            </button>
        </form>
    </div>
    
    <script>
        // Focus on username field when page loads
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('username').focus();
        });
        
        // Clear any error messages after 5 seconds
        setTimeout(function() {
            const errorAlert = document.querySelector('.alert-error');
            if (errorAlert) {
                errorAlert.style.opacity = '0';
                errorAlert.style.transition = 'opacity 0.5s ease';
                setTimeout(() => errorAlert.remove(), 500);
            }
        }, 5000);
    </script>
</body>
</html>
