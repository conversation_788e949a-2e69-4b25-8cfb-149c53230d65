<?php
/**
 * Configuration file
 * Loads environment variables from .env file
 */

class EnvLoader {
    public static function load($path = '.env') {
        if (!file_exists($path)) {
            throw new Exception(".env file not found at: $path");
        }
        
        $lines = file($path, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        
        foreach ($lines as $line) {
            // <PERSON><PERSON> comments
            if (strpos(trim($line), '#') === 0) {
                continue;
            }
            
            // Parse key=value pairs
            if (strpos($line, '=') !== false) {
                list($key, $value) = explode('=', $line, 2);
                $key = trim($key);
                $value = trim($value);
                
                // Remove quotes if present
                if (preg_match('/^(["\'])(.*)\\1$/', $value, $matches)) {
                    $value = $matches[2];
                }
                
                // Set environment variable
                $_ENV[$key] = $value;
                putenv("$key=$value");
            }
        }
    }
}

// Load environment variables - use absolute path resolution
$envPath = dirname(__DIR__) . '/.env';
$envExamplePath = dirname(__DIR__) . '/.env.example';

try {
    EnvLoader::load($envPath);
} catch (Exception $e) {
    // Try .env.example as fallback for development
    try {
        EnvLoader::load($envExamplePath);
        echo "⚠️  Warning: Using .env.example file. Please create a .env file with your actual credentials.\n";
    } catch (Exception $e2) {
        die("❌ Error: Neither .env nor .env.example file found. Please create a .env file with your database and API credentials.\n");
    }
}

// Define constants from environment variables
define('PRODUCT_HUNT_API_KEY', $_ENV['PRODUCT_HUNT_API_KEY'] ?? '');
define('DB_NAME', $_ENV['DB_NAME'] ?? '');
define('DB_USER', $_ENV['DB_USER'] ?? '');
define('DB_PASSWORD', $_ENV['DB_PASSWORD'] ?? '');
define('DB_HOST', $_ENV['DB_HOST'] ?? 'localhost');
define('DB_PORT', $_ENV['DB_PORT'] ?? '3306');
define('WEB_USER', $_ENV['WEB_USER'] ?? '');
define('WEB_PASSWORD', $_ENV['WEB_PASSWORD'] ?? '');

// Validate required configuration
function validateConfig() {
    $required = [
        'PRODUCT_HUNT_API_KEY' => PRODUCT_HUNT_API_KEY,
        'DB_NAME' => DB_NAME,
        'DB_USER' => DB_USER,
        'WEB_USER' => WEB_USER,
        'WEB_PASSWORD' => WEB_PASSWORD
    ];

    // DB_PASSWORD can be empty for local development, so we only check if it's defined

    $missing = [];

    // Check required fields that cannot be empty
    foreach ($required as $key => $value) {
        if (empty($value)) {
            $missing[] = $key;
        }
    }

    // Check DB_PASSWORD - it must be defined but can be empty
    if (!defined('DB_PASSWORD')) {
        $missing[] = 'DB_PASSWORD';
    }

    if (!empty($missing)) {
        die("❌ Error: Missing required environment variables: " . implode(', ', $missing) . "\n");
    }
}

// Uncomment the line below if you want to validate config on every include
// validateConfig();
?>
