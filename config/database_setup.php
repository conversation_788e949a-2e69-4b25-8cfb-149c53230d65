<?php
/**
 * Database Setup Script
 * Creates the necessary database structure for Product Hunt data
 */

require_once 'config.php';

class DatabaseSetup {
    private $pdo;
    
    public function __construct() {
        $this->connectToDatabase();
    }
    
    private function connectToDatabase() {
        try {
            $dsn = "mysql:host=" . DB_HOST . ";port=" . DB_PORT . ";dbname=" . DB_NAME . ";charset=utf8mb4";
            $this->pdo = new PDO($dsn, DB_USER, DB_PASSWORD, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
            ]);
            echo "✅ Connected to database successfully\n";
        } catch (PDOException $e) {
            die("❌ Database connection failed: " . $e->getMessage() . "\n");
        }
    }
    
    public function createTables() {
        $this->createProductsTable();
        $this->createCategoriesTable();
        $this->createProductCategoriesTable();
        $this->createOverridesTable();
        $this->createScheduleLogsTable();
        $this->createFetchProgressTable();
        $this->createProductReviewsTable();
        $this->createRateLimitTable();
        $this->updateSchema();
    }

    private function updateSchema() {
        // First rename website column to url if it exists
        try {
            $stmt = $this->pdo->prepare("SHOW COLUMNS FROM products LIKE 'website'");
            $stmt->execute();

            if ($stmt->rowCount() > 0) {
                $this->pdo->exec("ALTER TABLE products CHANGE COLUMN website url VARCHAR(500)");
                echo "✅ Renamed website column to url\n";
            }
        } catch (PDOException $e) {
            echo "⚠️  Warning: Could not rename website column: " . $e->getMessage() . "\n";
        }

        // Add new columns if they don't exist (in order)
        $this->addColumnIfNotExists('tagline', 'TEXT', 'AFTER name');
        $this->addColumnIfNotExists('comments_count', 'INT DEFAULT 0', 'AFTER votes_count');
        $this->addColumnIfNotExists('product_url', 'VARCHAR(500)', 'AFTER review_rating');
        $this->addColumnIfNotExists('external_url', 'VARCHAR(500)', 'AFTER url');
        $this->addColumnIfNotExists('external_url_status', 'VARCHAR(50)', 'AFTER external_url');
        $this->addColumnIfNotExists('url_checked', 'TINYINT(1) DEFAULT NULL', 'AFTER external_url_status');
    }

    private function createScheduleLogsTable() {
        $sql = "CREATE TABLE IF NOT EXISTS schedule_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            schedule_time VARCHAR(5) NOT NULL,
            target_type VARCHAR(20) NOT NULL,
            target_date DATE,
            status VARCHAR(20) NOT NULL,
            products_fetched INT DEFAULT 0,
            error_message TEXT,
            started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            completed_at TIMESTAMP NULL,
            INDEX idx_schedule_time (schedule_time),
            INDEX idx_target_type (target_type),
            INDEX idx_target_date (target_date),
            INDEX idx_status (status),
            INDEX idx_started_at (started_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

        try {
            $this->pdo->exec($sql);
            echo "✅ Schedule logs table created/verified successfully\n";
        } catch (PDOException $e) {
            echo "❌ Error creating schedule logs table: " . $e->getMessage() . "\n";
        }
    }

    private function createFetchProgressTable() {
        $sql = "CREATE TABLE IF NOT EXISTS fetch_progress (
            id INT AUTO_INCREMENT PRIMARY KEY,
            target_date DATE NOT NULL,
            pagination_cursor VARCHAR(500),
            page_count INT DEFAULT 0,
            products_fetched INT DEFAULT 0,
            status ENUM('in_progress', 'completed', 'rate_limited') DEFAULT 'in_progress',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_date (target_date),
            INDEX idx_status (status),
            INDEX idx_target_date (target_date)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

        try {
            $this->pdo->exec($sql);
            echo "✅ Fetch progress table created/verified successfully\n";
        } catch (PDOException $e) {
            echo "❌ Error creating fetch progress table: " . $e->getMessage() . "\n";
        }
    }

    private function createProductReviewsTable() {
        $sql = "CREATE TABLE IF NOT EXISTS product_reviews (
            id INT AUTO_INCREMENT PRIMARY KEY,
            product_id VARCHAR(20) NOT NULL,
            reviewed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
            INDEX idx_product_id (product_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

        try {
            $this->pdo->exec($sql);
            echo "✅ Product reviews table created/verified successfully\n";
        } catch (PDOException $e) {
            echo "❌ Error creating product reviews table: " . $e->getMessage() . "\n";
        }
    }

    private function createRateLimitTable() {
        $sql = "CREATE TABLE IF NOT EXISTS rate_limit_status (
            id INT AUTO_INCREMENT PRIMARY KEY,
            credits_remaining INT NOT NULL DEFAULT 6250,
            reset_time TIMESTAMP NULL,
            last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_reset_time (reset_time)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

        try {
            $this->pdo->exec($sql);
            echo "✅ Rate limit status table created/verified successfully\n";

            // Insert initial record if table is empty
            $checkSql = "SELECT COUNT(*) FROM rate_limit_status";
            $count = $this->pdo->query($checkSql)->fetchColumn();
            if ($count == 0) {
                $insertSql = "INSERT INTO rate_limit_status (credits_remaining) VALUES (6250)";
                $this->pdo->exec($insertSql);
                echo "✅ Initial rate limit record created\n";
            }
        } catch (PDOException $e) {
            echo "❌ Error creating rate limit status table: " . $e->getMessage() . "\n";
        }
    }

    private function addColumnIfNotExists($columnName, $columnType, $position = '') {
        try {
            $stmt = $this->pdo->prepare("SHOW COLUMNS FROM products LIKE ?");
            $stmt->execute([$columnName]);

            if ($stmt->rowCount() == 0) {
                $sql = "ALTER TABLE products ADD COLUMN $columnName $columnType";
                if ($position) {
                    $sql .= " $position";
                }
                $this->pdo->exec($sql);
                echo "✅ Added $columnName column to products table\n";
            }
        } catch (PDOException $e) {
            echo "⚠️  Warning: Could not add $columnName column: " . $e->getMessage() . "\n";
        }
    }
    
    private function createProductsTable() {
        $sql = "CREATE TABLE IF NOT EXISTS products (
            id VARCHAR(20) PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            tagline TEXT,
            description TEXT,
            votes_count INT DEFAULT 0,
            comments_count INT DEFAULT 0,
            review_rating DECIMAL(3,2),
            product_url VARCHAR(500),
            date_created DATE,
            url VARCHAR(500),
            external_url VARCHAR(500),
            external_url_status VARCHAR(50),
            url_checked TINYINT(1) DEFAULT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_date_created (date_created),
            INDEX idx_review_rating (review_rating),
            INDEX idx_votes_count (votes_count)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        try {
            $this->pdo->exec($sql);
            echo "✅ Products table created/verified successfully\n";
        } catch (PDOException $e) {
            echo "❌ Error creating products table: " . $e->getMessage() . "\n";
        }
    }
    
    private function createCategoriesTable() {
        $sql = "CREATE TABLE IF NOT EXISTS categories (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL UNIQUE,
            slug VARCHAR(100) NOT NULL UNIQUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_slug (slug)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        try {
            $this->pdo->exec($sql);
            echo "✅ Categories table created/verified successfully\n";
        } catch (PDOException $e) {
            echo "❌ Error creating categories table: " . $e->getMessage() . "\n";
        }
    }
    
    private function createProductCategoriesTable() {
        $sql = "CREATE TABLE IF NOT EXISTS product_categories (
            product_id VARCHAR(20),
            category_id INT,
            PRIMARY KEY (product_id, category_id),
            FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
            FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        try {
            $this->pdo->exec($sql);
            echo "✅ Product categories junction table created/verified successfully\n";
        } catch (PDOException $e) {
            echo "❌ Error creating product categories table: " . $e->getMessage() . "\n";
        }
    }

    private function createOverridesTable() {
        // First, check if we need to migrate from the old table
        $this->migrateManualUrlOverridesToOverrides();

        // Create the new table structure (will be skipped if already exists after migration)
        $sql = "CREATE TABLE IF NOT EXISTS overrides (
            id INT AUTO_INCREMENT PRIMARY KEY,
            product_id VARCHAR(20) NOT NULL,
            type VARCHAR(50) NOT NULL DEFAULT 'url',
            rule VARCHAR(2000) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
            UNIQUE KEY unique_product_type_override (product_id, type)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

        try {
            $this->pdo->exec($sql);
            echo "✅ Overrides table created/verified successfully\n";
        } catch (PDOException $e) {
            echo "❌ Error creating overrides table: " . $e->getMessage() . "\n";
        }
    }

    private function migrateManualUrlOverridesToOverrides() {
        try {
            // Check if old table exists
            $stmt = $this->pdo->query("SHOW TABLES LIKE 'manual_url_overrides'");
            if ($stmt->rowCount() == 0) {
                // Old table doesn't exist, no migration needed
                return;
            }

            // Check if new table already exists
            $stmt = $this->pdo->query("SHOW TABLES LIKE 'overrides'");
            if ($stmt->rowCount() > 0) {
                // New table already exists, migration already done
                echo "✅ Migration already completed - overrides table exists\n";
                return;
            }

            echo "🔄 Migrating manual_url_overrides to overrides table...\n";

            // Rename the table
            $this->pdo->exec("ALTER TABLE manual_url_overrides RENAME TO overrides");
            echo "✅ Table renamed from manual_url_overrides to overrides\n";

            // Add the type column with default value 'url'
            $this->pdo->exec("ALTER TABLE overrides ADD COLUMN type VARCHAR(50) NOT NULL DEFAULT 'url' AFTER product_id");
            echo "✅ Added type column with default value 'url'\n";

            // Rename the manual_url column to rule
            $this->pdo->exec("ALTER TABLE overrides CHANGE manual_url rule VARCHAR(2000) NOT NULL");
            echo "✅ Renamed manual_url column to rule\n";

            // Update the unique key constraint (check if it exists first)
            try {
                $this->pdo->exec("ALTER TABLE overrides DROP INDEX unique_product_override");
                echo "✅ Dropped old unique key constraint\n";
            } catch (PDOException $e) {
                echo "ℹ️  Old unique key constraint not found or already removed\n";
            }

            try {
                $this->pdo->exec("ALTER TABLE overrides ADD UNIQUE KEY unique_product_type_override (product_id, type)");
                echo "✅ Added new unique key constraint to include type\n";
            } catch (PDOException $e) {
                echo "ℹ️  New unique key constraint already exists\n";
            }

            echo "✅ Migration from manual_url_overrides to overrides completed successfully\n";

        } catch (PDOException $e) {
            echo "❌ Error during migration: " . $e->getMessage() . "\n";
            // Don't throw the error, let the system continue
        }
    }

    public function checkTablesExist() {
        $tables = ['products', 'categories', 'product_categories'];
        $existingTables = [];
        
        foreach ($tables as $table) {
            $stmt = $this->pdo->prepare("SHOW TABLES LIKE ?");
            $stmt->execute([$table]);
            if ($stmt->rowCount() > 0) {
                $existingTables[] = $table;
            }
        }
        
        return $existingTables;
    }
    
    public function getTableInfo($tableName) {
        try {
            $stmt = $this->pdo->prepare("DESCRIBE $tableName");
            $stmt->execute();
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            echo "❌ Error getting table info for $tableName: " . $e->getMessage() . "\n";
            return [];
        }
    }
}

// Helper functions for category management
class CategoryManager {
    private $pdo;
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
    }
    
    public function addCategory($name) {
        $slug = $this->createSlug($name);
        
        try {
            $stmt = $this->pdo->prepare("INSERT IGNORE INTO categories (name, slug) VALUES (?, ?)");
            $stmt->execute([$name, $slug]);
            
            // Get the category ID
            $stmt = $this->pdo->prepare("SELECT id FROM categories WHERE slug = ?");
            $stmt->execute([$slug]);
            return $stmt->fetchColumn();
        } catch (PDOException $e) {
            echo "❌ Error adding category '$name': " . $e->getMessage() . "\n";
            return false;
        }
    }
    
    public function getCategoryId($name) {
        $slug = $this->createSlug($name);
        
        try {
            $stmt = $this->pdo->prepare("SELECT id FROM categories WHERE slug = ?");
            $stmt->execute([$slug]);
            return $stmt->fetchColumn();
        } catch (PDOException $e) {
            return false;
        }
    }
    
    private function createSlug($text) {
        $text = strtolower($text);
        $text = preg_replace('/[^a-z0-9\s-]/', '', $text);
        $text = preg_replace('/[\s-]+/', '-', $text);
        return trim($text, '-');
    }
}

// Run the setup if this file is executed directly
if (basename(__FILE__) == basename($_SERVER["SCRIPT_NAME"])) {
    echo "🚀 Starting database setup...\n\n";
    
    $setup = new DatabaseSetup();
    
    echo "📋 Checking existing tables...\n";
    $existingTables = $setup->checkTablesExist();
    if (!empty($existingTables)) {
        echo "Found existing tables: " . implode(', ', $existingTables) . "\n\n";
    } else {
        echo "No existing tables found.\n\n";
    }
    
    echo "🔨 Creating/verifying database structure...\n";
    $setup->createTables();
    
    echo "\n✨ Database setup completed!\n";
}
?>
