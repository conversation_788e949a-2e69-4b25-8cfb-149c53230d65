{"description": "Simple scheduling configuration for continuous product fetching", "version": "2.0", "settings": {"timezone": "America/Los_Angeles", "check_interval_minutes": 5, "rate_limit_buffer_seconds": 15}, "daily_schedule": [{"time": "00:15", "target": "today", "description": "Fetch today's products at 12:15 AM PT"}, {"time": "06:00", "target": "today", "description": "Fetch today's products at 6:00 AM PT"}, {"time": "12:00", "target": "today", "description": "Fetch today's products at 12:00 PM PT"}, {"time": "18:00", "target": "today", "description": "Fetch today's products at 6:00 PM PT"}, {"time": "23:45", "target": "today", "description": "Fetch today's products at 11:45 PM PT"}], "targets": {"today": {"description": "Today's products (PT timezone)", "strategy": "fetch_current_date"}, "historical": {"description": "Historical backfill - continues from last successful date", "strategy": "fetch_next_historical_date", "direction": "backward", "stop_at": "2013-01-01"}}}