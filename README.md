# Product Hunt Dashboard

A comprehensive PHP application that fetches launched products from Product Hunt's GraphQL API, resolves their external URLs with Playwright fallback, and provides a web dashboard for management. The system is organized into separate modules for better maintainability.

## Project Structure

```
/
├── index.php              # Main dashboard interface
├── login.php              # Authentication page
├── logout.php             # Logout handler
├── README.md              # This file
├── STRUCTURE.md           # Detailed structure documentation
├── config/
│   ├── config.php         # Database and application configuration
│   └── database_setup.php # Database schema and setup
├── api/
│   └── manual_url_api.php # API for manual URL overrides
├── scripts/
│   ├── fetch_products.php # Product Hunt API data fetcher
│   ├── resolve_urls.php   # URL resolver with Playwright fallback
│   ├── analysis/          # Data analysis scripts
│   └── maintenance/       # Maintenance and cleanup scripts
├── includes/
│   ├── auth.php           # Authentication system
│   └── url_utils.php      # URL processing utilities
└── assets/
    └── img/               # Images and static assets
```

## Architecture

The system consists of separate processes:

### 1. **Data Fetching** (`fetch_products.php`)
- Fetches product data from Product Hunt's GraphQL API
- Stores products in the database without URL resolution
- Fast and focused on API data collection

### 2. **URL Resolution** (`resolve_urls.php`)
- Resolves external URLs for products in the database
- Can be run independently with various filtering options
- Handles redirect chains and URL cleaning

## Features

### Data Fetching
- 🗄️ **Database Setup**: Automatically creates the necessary MySQL database structure
- 📡 **API Integration**: Fetches data from Product Hunt's GraphQL API with pagination support
- 🏷️ **Category Management**: Handles multiple categories per product using a normalized database structure
- 🔄 **Update Support**: Updates existing products with fresh data instead of skipping them
- 📊 **Pagination**: Fetches ALL products (not just the first 20) using automatic pagination
- 🚦 **Rate Limit Monitoring**: Displays API rate limit status and handles rate limit errors gracefully
- 📄 **Page Limiting**: Support for limiting API fetches to specific number of pages (testing)

### URL Resolution
- 🔗 **URL Redirect Resolution**: Follows redirect chains to resolve final destination URLs
- 🧹 **Clean URLs**: Automatically removes URL parameters (UTM tracking, etc.) for cleaner data
- 🎯 **Selective Resolution**: Only resolves unresolved URLs by default
- 🔄 **Force Mode**: Option to re-resolve all URLs
- 📊 **Flexible Filtering**: Date-based and limit-based filtering options

### Web Interface
- 🌐 **DataTables Interface**: Powerful interface with sorting, search, and responsive design
- 📅 **Date Filtering**: Filter products by launch date with reset functionality
- 🔍 **Status Filtering**: Filter by URL resolution status (failed, unprocessed)
- 📱 **Mobile Responsive**: Works well on all device sizes

### General
- ⚙️ **Environment Configuration**: Uses `.env` file for secure credential management
- 🛡️ **Safety Features**: Rate limiting, delays, and user agent rotation for respectful scraping

## Database Structure

The application creates three main tables:

### `products`
- `id` (VARCHAR(20)) - Product Hunt product ID
- `name` (VARCHAR(255)) - Product name
- `tagline` (TEXT) - Product tagline from Product Hunt
- `description` (TEXT) - Product description
- `votes_count` (INT) - Number of votes received
- `comments_count` (INT) - Number of comments received
- `review_rating` (DECIMAL(3,2)) - Average review rating
- `product_url` (VARCHAR(500)) - Product Hunt page URL
- `date_created` (DATE) - Launch date
- `url` (VARCHAR(500)) - Product website URL (cleaned, no parameters)
- `external_url` (VARCHAR(500)) - External/final URL (nullable)
- `external_url_status` (VARCHAR(50)) - Status of external URL (nullable)
- `url_checked` (TINYINT(1)) - Whether URL has been checked (nullable)

### `categories`
- `id` (INT) - Auto-increment primary key
- `name` (VARCHAR(100)) - Category name
- `slug` (VARCHAR(100)) - URL-friendly category identifier

### `product_categories`
- Junction table linking products to categories (many-to-many relationship)

## Setup

1. **Environment Configuration**
   ```bash
   cp .env.example .env
   ```
   
   Edit `.env` with your actual credentials:
   ```
   PRODUCT_HUNT_API_KEY=your_api_key_here
   DB_NAME=your_database_name
   DB_USER=your_db_username
   DB_PASSWORD=your_db_password
   DB_HOST=localhost
   DB_PORT=3306
   ```

2. **Database Setup**
   ```bash
   php config/database_setup.php
   ```

3. **Fetch Products**
   ```bash
   # Fetch today's products
   php fetch_today_products.php

   # Fetch products from a specific date
   php fetch_today_products.php --date=2024-01-15

   # Fetch products from a date range
   php fetch_today_products.php --start-date=2024-01-01 --end-date=2024-01-31

   # Limit to specific number of pages (for testing)
   php fetch_today_products.php --max-pages=2
   ```

## Usage

### Setting up the Database
Run the database setup script to create the necessary tables:
```bash
php config/database_setup.php
```

### 1. Fetching Products (`scripts/fetch_products.php`)

**Fetch today's products (default):**
```bash
php scripts/fetch_products.php
```

**Fetch products from a specific date:**
```bash
php scripts/fetch_products.php --date=2024-01-15
```

**Fetch products from a date range:**
```bash
php scripts/fetch_products.php --start-date=2024-01-01 --end-date=2024-01-31
```

**Limit to specific number of pages (for testing):**
```bash
php scripts/fetch_products.php --max-pages=2
```

**Combine parameters:**
```bash
php scripts/fetch_products.php --date=2024-01-15 --max-pages=3
```

**Get help:**
```bash
php scripts/fetch_products.php --help
```

### 2. Resolving URLs (`scripts/resolve_urls.php`)

**Resolve unresolved URLs (default behavior):**
```bash
php scripts/resolve_urls.php
```

**Resolve URLs for products from a specific date:**
```bash
php scripts/resolve_urls.php --date=2024-01-15
```

**Resolve URLs for products from a date range:**
```bash
php scripts/resolve_urls.php --start-date=2024-01-01 --end-date=2024-01-31
```

**Force resolve all URLs (including already resolved):**
```bash
php scripts/resolve_urls.php --force
```

**Limit URL resolution to specific number of products:**
```bash
php scripts/resolve_urls.php --limit=50
```

**Use Playwright fallback for failed URLs:**
```bash
php scripts/resolve_urls.php --playwright --limit=50
```

**Resolve URLs starting from newest products:**
```bash
php scripts/resolve_urls.php --all --limit=100
```

**Combine options:**
```bash
php scripts/resolve_urls.php --date=2024-01-15 --force --playwright --limit=25
```

**Get help:**
```bash
php scripts/resolve_urls.php --help
```

### 3. Programmatic Usage

You can also use the classes in your own PHP code:

**Data Fetching:**
```php
<?php
require_once 'config.php';
require_once 'database_setup.php';
require_once 'fetch_products.php';

// Setup database
$setup = new DatabaseSetup();
$setup->createTables();

// Fetch and save products
$fetcher = new ProductHuntAPI();
$products = $fetcher->fetchAllTodaysPosts();
$results = $fetcher->saveProductsToDatabase($products);

echo "Saved {$results['inserted']} new products\n";
echo "Updated {$results['updated']} existing products\n";
?>
```

**URL Resolution:**
```php
<?php
require_once 'config.php';
require_once 'resolve_urls.php';

// Resolve URLs with options
$options = [
    'date' => '2024-01-15',
    'limit' => 50,
    'force' => false
];

$resolver = new URLResolver($options);
$processed = $resolver->resolveUrls();

echo "Processed {$processed} URLs\n";
?>
```

### 4. Recommended Workflow

For optimal performance and reliability, use this two-phase approach:

**Phase 1: Fast Data Collection**
```bash
# Fetch today's products quickly (no URL resolution)
php scripts/fetch_products.php
```

**Phase 2: URL Resolution**
```bash
# Resolve URLs for unresolved products
php scripts/resolve_urls.php --limit=100

# Or with Playwright fallback for problematic URLs
php scripts/resolve_urls.php --playwright --limit=100
```

**For Historical Data:**
```bash
# Fetch historical data
php scripts/fetch_products.php --start-date=2024-01-01 --end-date=2024-01-31

# Then resolve URLs for that period
php scripts/resolve_urls.php --start-date=2024-01-01 --end-date=2024-01-31
```

**For Maintenance:**
```bash
# Re-resolve failed URLs with Playwright fallback
php scripts/resolve_urls.php --recheck-down --playwright --limit=50
```

## Requirements

- PHP 7.4 or higher
- MySQL 5.7 or higher
- cURL extension enabled
- PDO MySQL extension

## API Key

To get a Product Hunt API key:
1. Visit [Product Hunt API](https://api.producthunt.com/v2/docs)
2. Create an account and generate an API token
3. Add the token to your `.env` file

## Rate Limiting

Product Hunt's API has rate limits (typically 6,250 requests per hour). The application:
- Displays current rate limit status after each API call
- Handles rate limit errors gracefully by stopping and showing progress
- Uses pagination with 20 products per request to minimize API calls
- Includes 1-second delays between requests to be respectful to the API

If you hit the rate limit, wait for the reset time and run the script again. It will update existing products and continue fetching new ones.

## Web Interface

Access the web interface to view all products:

```bash
# Start local PHP server
php -S localhost:8000

# Open in browser
http://localhost:8000
```

**Features:**
- 📊 **Statistics Dashboard**: Total products, average votes, max votes, launch days, first fetch date
- 🔍 **Advanced Search**: Search across all product fields with real-time filtering
- 📄 **DataTables Integration**: Professional table with pagination, sorting, and responsive design
- 🔄 **Multi-Column Sorting**: Primary sort by launch date, secondary by votes (customizable)
- 📱 **Responsive Design**: Horizontal scrolling on mobile, auto-adjusting headers
- 🕐 **Live Time Display**: Real-time Pacific Time (Product Hunt's timezone)
- 🔗 **Complete Data**: All product information including categories, URLs, timestamps
- 📋 **Export Ready**: DataTables supports CSV/Excel export (can be enabled)



## URL Cleaning

The system automatically removes URL parameters to provide clean, consistent URLs:
- **Removes UTM tracking** parameters (utm_campaign, utm_medium, utm_source, etc.)
- **Removes query parameters** and fragments from URLs
- **Preserves core URL structure** (scheme, host, port, path)
- **Automatic processing** for all new products fetched from API
- **Cleans both website URLs and Product Hunt URLs**

### Clean Existing URLs

Use the URL cleaner scripts to remove parameters from existing database records:

**For website URLs:**
```bash
# Show website URL statistics
php scripts/maintenance/clean_existing_urls.php stats

# Test website URL cleaning on sample data
php scripts/maintenance/clean_existing_urls.php test 5

# Clean all website URLs by removing parameters
php scripts/maintenance/clean_existing_urls.php clean
```

**For Product Hunt URLs:**
```bash
# Show Product Hunt URL statistics
php scripts/maintenance/clean_product_urls.php stats

# Test Product Hunt URL cleaning on sample data
php scripts/maintenance/clean_product_urls.php test 3

# Clean all Product Hunt URLs by removing parameters
php scripts/maintenance/clean_product_urls.php clean
```

## URL Redirect Resolution

The system automatically resolves redirect chains for product website URLs to find the final destination:

### How It Works
- **Automatic Processing**: Runs during product fetching for all new/updated products
- **Redirect Following**: Follows 301/302 redirects up to 10 hops with 30-second timeout
- **Smart Detection**: Only processes URLs that match common redirect patterns (producthunt.com/r/, bit.ly/, etc.)
- **Status Tracking**: Stores resolution status in `external_url_status` field
- **Error Handling**: Gracefully handles 404, 403, 429, 503, timeouts, and DNS errors

### Database Fields
- **`external_url`**: Final resolved URL after following all redirects
- **`external_url_status`**: 'true' for successful resolution, 'false' for failures

### Examples
```
Original URL: https://www.producthunt.com/r/ABC123
Resolved URL: https://example.com/product?ref=producthunt
Status: true

Original URL: https://bit.ly/broken-link
Resolved URL: NULL
Status: false
```

### Rate Limiting & Limitations
- **Respectful delays**: 0.5 second delay between URL resolutions
- **Browser-like headers**: Uses realistic User-Agent and headers to avoid blocking
- **Timeout handling**: 30-second timeout per URL to prevent hanging
- **Domain limitations**: Some domains (e.g., Visual Studio Marketplace) have strict bot detection and may block automated requests even with browser headers

## Utility Scripts

- **`scripts/analysis/check_data.php`** - View database statistics and sample products
- **`index.php`** - Web interface to browse all products
- **`scripts/maintenance/clean_existing_urls.php`** - Remove URL parameters from existing website URLs
- **`scripts/maintenance/clean_product_urls.php`** - Remove URL parameters from existing Product Hunt URLs


## Error Handling

The application includes comprehensive error handling for:
- Database connection issues
- API authentication problems
- API rate limiting
- Network timeouts
- Invalid JSON responses
- Missing environment variables

## Automation

You can set up a cron job to automatically fetch products daily:

```bash
# Add to crontab (crontab -e)
# Run every day at 9 AM
0 9 * * * /usr/bin/php /path/to/your/project/scripts/fetch_products.php >> /var/log/producthunt_fetch.log 2>&1
```

## License

This project is open source and available under the MIT License.
