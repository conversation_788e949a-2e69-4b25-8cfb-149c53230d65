<?php
/**
 * Reset Historical Fetch Script
 * 
 * This script removes all cursors and schedule logs to allow for fresh historical fetch.
 * It should be run when you want to restart historical data fetching from the beginning.
 */

require_once __DIR__ . '/../config/config.php';

echo "🔄 Reset Historical Fetch\n";
echo "=========================\n\n";

try {
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
        DB_USER,
        DB_PASSWORD,
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    
    echo "✅ Database connection successful\n\n";
    
    // Get current status before reset
    echo "📊 Current Status Before Reset:\n";
    
    // Check fetch_progress
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM fetch_progress");
    $progressCount = $stmt->fetchColumn();
    echo "   - Fetch progress records: " . number_format($progressCount) . "\n";
    
    // Check schedule_logs
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM schedule_logs");
    $scheduleCount = $stmt->fetchColumn();
    echo "   - Schedule log records: " . number_format($scheduleCount) . "\n";
    
    // Show recent progress
    $stmt = $pdo->query("
        SELECT target_date, status, page_count, products_fetched, updated_at 
        FROM fetch_progress 
        ORDER BY updated_at DESC 
        LIMIT 5
    ");
    $recentProgress = $stmt->fetchAll();
    
    if (!empty($recentProgress)) {
        echo "\n   Recent fetch progress:\n";
        foreach ($recentProgress as $progress) {
            echo "     - {$progress['target_date']}: {$progress['status']} ({$progress['page_count']} pages, {$progress['products_fetched']} products)\n";
        }
    }
    
    echo "\n🗑️  Performing Reset:\n";
    
    // Clear fetch_progress table
    $stmt = $pdo->prepare("DELETE FROM fetch_progress");
    $stmt->execute();
    $deletedProgress = $stmt->rowCount();
    echo "   ✅ Deleted $deletedProgress fetch progress records\n";
    
    // Clear schedule_logs table
    $stmt = $pdo->prepare("DELETE FROM schedule_logs");
    $stmt->execute();
    $deletedSchedule = $stmt->rowCount();
    echo "   ✅ Deleted $deletedSchedule schedule log records\n";
    
    // Clear any stored rate limit info
    $stmt = $pdo->prepare("DELETE FROM rate_limit_info WHERE id = 1");
    $stmt->execute();
    $deletedRateLimit = $stmt->rowCount();
    if ($deletedRateLimit > 0) {
        echo "   ✅ Cleared stored rate limit info\n";
    }
    
    echo "\n🎯 Reset Complete!\n";
    echo "   - All pagination cursors cleared\n";
    echo "   - All schedule logs cleared\n";
    echo "   - Historical fetch will start from the beginning\n";
    echo "   - Today's fetch progress preserved (if any)\n";
    
    echo "\n📋 Next Steps:\n";
    echo "   1. Run continuous_fetch.php to start fresh historical fetch\n";
    echo "   2. Historical fetch will begin from yesterday and work backwards\n";
    echo "   3. Each date will be fetched completely before moving to the next\n";
    
} catch (PDOException $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
    exit(1);
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n✅ Historical fetch reset completed successfully!\n";
?>
