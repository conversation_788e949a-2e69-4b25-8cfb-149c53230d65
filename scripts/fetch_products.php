<?php
/**
 * Product Hunt API Fetcher
 * Fetches today's launched products from Product Hunt GraphQL API
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../config/database_setup.php';
require_once __DIR__ . '/../includes/url_utils.php';

class ProductHuntAPI {
    private $apiUrl = 'https://api.producthunt.com/v2/api/graphql';
    private $apiKey;
    private $pdo;
    private $categoryManager;
    private $rateLimitInfo = [];
    private $batchSize = 20; // Products per API request
    private $targetDate = null;
    private $startDate = null;
    private $endDate = null;
    private $maxPages = null;

    public function __construct($options = []) {
        validateConfig();
        $this->apiKey = PRODUCT_HUNT_API_KEY;
        $this->connectToDatabase();
        $this->categoryManager = new CategoryManager($this->pdo);

        // Set date filters
        if (isset($options['date'])) {
            $this->targetDate = $options['date'];
        } elseif (isset($options['start_date']) && isset($options['end_date'])) {
            $this->startDate = $options['start_date'];
            $this->endDate = $options['end_date'];
        } else {
            // Default to today
            $this->targetDate = date('Y-m-d');
        }

        // Set max pages limit
        if (isset($options['max_pages']) && $options['max_pages'] > 0) {
            $this->maxPages = $options['max_pages'];
            echo "🔢 Limiting fetch to {$this->maxPages} pages\n";
        }
    }
    
    private function connectToDatabase() {
        try {
            $dsn = "mysql:host=" . DB_HOST . ";port=" . DB_PORT . ";dbname=" . DB_NAME . ";charset=utf8mb4";
            $this->pdo = new PDO($dsn, DB_USER, DB_PASSWORD, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
            ]);
        } catch (PDOException $e) {
            die("❌ Database connection failed: " . $e->getMessage() . "\n");
        }
    }
    
    public function fetchAllTodaysPosts() {
        $allProducts = [];
        $hasNextPage = true;
        $cursor = null;
        $pageCount = 0;

        // Check for existing progress and resume if needed
        $progress = $this->getExistingProgress();
        if ($progress) {
            $cursor = $progress['pagination_cursor'];
            $pageCount = $progress['page_count'];
            echo "🔄 Resuming fetch from page " . ($pageCount + 1) . " (cursor: " . substr($cursor ?? 'null', 0, 20) . "...)\n";
            echo "📊 Previously fetched {$progress['products_fetched']} products\n";
        } else {
            echo "🆕 Starting fresh fetch\n";
            $this->initializeProgress();
        }

        while ($hasNextPage) {
            $pageCount++;
            echo "📄 Fetching page $pageCount...\n";

            // Check if we've reached the max pages limit
            if ($this->maxPages && $pageCount > $this->maxPages) {
                echo "🔢 Reached max pages limit ({$this->maxPages}), stopping fetch\n";
                break;
            }

            try {
                $pageData = $this->fetchPostsPage($cursor);

                if (empty($pageData['edges'])) {
                    break;
                }

                $allProducts = array_merge($allProducts, $pageData['edges']);
                $hasNextPage = $pageData['pageInfo']['hasNextPage'] ?? false;
                $cursor = $pageData['pageInfo']['endCursor'] ?? null;

                // Update progress after each successful page
                $this->updateProgress($cursor, $pageCount, count($allProducts), 'in_progress');

                $this->displayRateLimitInfo();

                // Small delay to be respectful to the API
                if ($hasNextPage) {
                    sleep(1);
                }

            } catch (Exception $e) {
                if (strpos($e->getMessage(), 'Rate limit reached') !== false) {
                    echo "⚠️  " . $e->getMessage() . "\n";
                    echo "📊 Successfully fetched " . count($allProducts) . " products before hitting rate limit.\n";

                    // Save progress with rate_limited status so we can resume
                    $this->updateProgress($cursor, $pageCount, count($allProducts), 'rate_limited');
                    echo "💾 Progress saved - will resume from page " . ($pageCount + 1) . " after rate limit reset\n";

                    break;
                } else {
                    throw $e; // Re-throw other exceptions
                }
            }
        }

        // Mark as completed if we finished successfully
        if (!$hasNextPage) {
            $this->updateProgress($cursor, $pageCount, count($allProducts), 'completed');
            echo "✅ Fetch completed successfully\n";
        }

        echo "✅ Fetched " . count($allProducts) . " products across $pageCount pages\n\n";
        return $allProducts;
    }

    private function fetchPostsPage($cursor = null) {
        $variables = [
            'first' => $this->batchSize
        ];

        if ($cursor) {
            $variables['after'] = $cursor;
        }

        // Add date filtering (Product Hunt uses Pacific Time)
        if ($this->targetDate) {
            // Convert to Pacific Time properly
            $startTime = new DateTime($this->targetDate . ' 00:00:00', new DateTimeZone('America/Los_Angeles'));
            $endTime = new DateTime($this->targetDate . ' 23:59:59', new DateTimeZone('America/Los_Angeles'));

            $variables['postedAfter'] = $startTime->format('c'); // ISO 8601 format
            $variables['postedBefore'] = $endTime->format('c');

            echo "🔍 Filtering for date {$this->targetDate} (Pacific Time)\n";
        } elseif ($this->startDate && $this->endDate) {
            $startTime = new DateTime($this->startDate . ' 00:00:00', new DateTimeZone('America/Los_Angeles'));
            $endTime = new DateTime($this->endDate . ' 23:59:59', new DateTimeZone('America/Los_Angeles'));

            $variables['postedAfter'] = $startTime->format('c');
            $variables['postedBefore'] = $endTime->format('c');

            echo "🔍 Filtering for date range {$this->startDate} to {$this->endDate} (Pacific Time)\n";
        }

        $query = [
            'query' => '
                query fetchPosts($first: Int, $after: String, $postedAfter: DateTime, $postedBefore: DateTime) {
                    posts(first: $first, after: $after, postedAfter: $postedAfter, postedBefore: $postedBefore) {
                        edges {
                            node {
                                id
                                name
                                tagline
                                description
                                votesCount
                                commentsCount
                                website
                                url
                                createdAt
                                reviewsRating
                                topics {
                                    edges {
                                        node {
                                            name
                                        }
                                    }
                                }
                            }
                        }
                        pageInfo {
                            hasNextPage
                            endCursor
                        }
                    }
                }
            ',
            'variables' => $variables
        ];
        
        $headers = [
            'Accept: application/json',
            'Content-Type: application/json',
            'Authorization: Bearer ' . $this->apiKey,
            'Host: api.producthunt.com'
        ];

        $responseHeaders = [];

        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $this->apiUrl,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($query),
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_SSL_VERIFYPEER => true,
            CURLOPT_HEADERFUNCTION => function($curl, $header) use (&$responseHeaders) {
                // Suppress unused parameter warning
                unset($curl);
                $len = strlen($header);
                $header = explode(':', $header, 2);
                if (count($header) < 2) return $len;

                $name = strtolower(trim($header[0]));
                $value = trim($header[1]);
                $responseHeaders[$name] = $value;

                return $len;
            }
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        // Store rate limit information - try different header formats
        $this->rateLimitInfo = [
            'limit' => $responseHeaders['x-ratelimit-limit'] ??
                      $responseHeaders['x-rate-limit-limit'] ??
                      $responseHeaders['ratelimit-limit'] ?? 'Unknown',
            'remaining' => $responseHeaders['x-ratelimit-remaining'] ??
                          $responseHeaders['x-rate-limit-remaining'] ??
                          $responseHeaders['ratelimit-remaining'] ?? 'Unknown',
            'reset' => $responseHeaders['x-ratelimit-reset'] ??
                      $responseHeaders['x-rate-limit-reset'] ??
                      $responseHeaders['ratelimit-reset'] ?? 'Unknown'
        ];

        // Also check if rate limit info is in the response body
        if (isset($data['errors'])) {
            foreach ($data['errors'] as $error) {
                if (isset($error['details']['limit'])) {
                    $this->rateLimitInfo['limit'] = $error['details']['limit'];
                    $this->rateLimitInfo['remaining'] = $error['details']['remaining'];
                    $this->rateLimitInfo['reset'] = time() + ($error['details']['reset_in'] ?? 0);
                }
            }
        }

        if (curl_error($ch)) {
            $error = curl_error($ch);
            curl_close($ch);
            throw new Exception("cURL error: $error");
        }

        curl_close($ch);
        
        if ($httpCode !== 200) {
            throw new Exception("API request failed with HTTP code: $httpCode. Response: $response");
        }
        
        $data = json_decode($response, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception("JSON decode error: " . json_last_error_msg());
        }
        
        if (isset($data['errors'])) {
            // Check if it's a rate limit error
            foreach ($data['errors'] as $error) {
                if (isset($error['error']) && $error['error'] === 'rate_limit_reached') {
                    $resetIn = $error['details']['reset_in'] ?? 'unknown';
                    throw new Exception("Rate limit reached. Reset in {$resetIn} seconds. Please wait and try again.");
                }
            }
            throw new Exception("GraphQL errors: " . json_encode($data['errors']));
        }

        return $data['data']['posts'] ?? [];
    }

    private function displayRateLimitInfo() {
        if (!empty($this->rateLimitInfo)) {
            echo "🚦 Rate limit: {$this->rateLimitInfo['remaining']}/{$this->rateLimitInfo['limit']} remaining";
            if ($this->rateLimitInfo['reset'] !== 'Unknown') {
                $resetTime = date('H:i:s', $this->rateLimitInfo['reset']);
                echo " (resets at $resetTime)";
            }
            echo "\n";
        }
    }
    
    public function saveProductsToDatabase($products) {
        $insertedCount = 0;
        $updatedCount = 0;
        $errorCount = 0;

        foreach ($products as $edge) {
            $product = $edge['node'];

            try {
                // Check if product already exists
                $exists = $this->productExists($product['id']);

                if ($exists) {
                    // Update existing product
                    $this->updateProduct($product);
                    echo "🔄 Updated product: {$product['name']}\n";
                    $updatedCount++;
                } else {
                    // Insert new product
                    $this->insertProduct($product);
                    echo "✅ Inserted product: {$product['name']}\n";
                    $insertedCount++;
                }

                // Handle categories/topics (always update these)
                if (isset($product['topics']['edges']) && !empty($product['topics']['edges'])) {
                    // Clear existing categories for this product
                    $this->clearProductCategories($product['id']);
                    // Assign new categories
                    $this->assignCategoriesToProduct($product['id'], $product['topics']['edges']);
                }

                // URL resolution is now handled by separate resolve_urls.php script

            } catch (Exception $e) {
                echo "❌ Error processing product '{$product['name']}': " . $e->getMessage() . "\n";
                $errorCount++;
            }
        }

        return [
            'inserted' => $insertedCount,
            'updated' => $updatedCount,
            'errors' => $errorCount
        ];
    }
    
    private function productExists($productId) {
        $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM products WHERE id = ?");
        $stmt->execute([$productId]);
        return $stmt->fetchColumn() > 0;
    }
    
    private function insertProduct($product) {
        $sql = "INSERT INTO products (
            id, name, tagline, description, votes_count, comments_count, review_rating,
            product_url, date_created, url
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        $stmt = $this->pdo->prepare($sql);

        // Parse the created date
        $dateCreated = null;
        if (isset($product['createdAt'])) {
            $dateCreated = date('Y-m-d', strtotime($product['createdAt']));
        }

        // Clean both URLs by removing parameters
        $websiteUrl = $this->cleanWebsiteUrl($product['website'] ?? '');
        $productUrl = $this->cleanWebsiteUrl($product['url'] ?? '');

        $stmt->execute([
            $product['id'],
            $product['name'] ?? '',
            $product['tagline'] ?? '',
            $product['description'] ?? '',
            $product['votesCount'] ?? 0,
            $product['commentsCount'] ?? 0,
            $product['reviewsRating'] ?? null,
            $productUrl,
            $dateCreated,
            $websiteUrl
        ]);
    }

    private function updateProduct($product) {
        $sql = "UPDATE products SET
            name = ?, tagline = ?, description = ?, votes_count = ?, comments_count = ?,
            review_rating = ?, product_url = ?, date_created = ?, url = ?,
            updated_at = CURRENT_TIMESTAMP
            WHERE id = ?";

        $stmt = $this->pdo->prepare($sql);

        // Parse the created date
        $dateCreated = null;
        if (isset($product['createdAt'])) {
            $dateCreated = date('Y-m-d', strtotime($product['createdAt']));
        }

        // Clean both URLs by removing parameters
        $websiteUrl = $this->cleanWebsiteUrl($product['website'] ?? '');
        $productUrl = $this->cleanWebsiteUrl($product['url'] ?? '');

        $stmt->execute([
            $product['name'] ?? '',
            $product['tagline'] ?? '',
            $product['description'] ?? '',
            $product['votesCount'] ?? 0,
            $product['commentsCount'] ?? 0,
            $product['reviewsRating'] ?? null,
            $productUrl,
            $dateCreated,
            $websiteUrl,
            $product['id']
        ]);
    }



    private function clearProductCategories($productId) {
        $stmt = $this->pdo->prepare("DELETE FROM product_categories WHERE product_id = ?");
        $stmt->execute([$productId]);
    }

    private function cleanWebsiteUrl($url) {
        if (empty($url)) {
            return '';
        }

        // Parse the URL
        $parsedUrl = parse_url($url);

        if (!$parsedUrl) {
            return $url; // Return original if parsing fails
        }

        // Rebuild URL without query parameters and fragments
        $cleanUrl = '';

        if (isset($parsedUrl['scheme'])) {
            $cleanUrl .= $parsedUrl['scheme'] . '://';
        }

        if (isset($parsedUrl['user'])) {
            $cleanUrl .= $parsedUrl['user'];
            if (isset($parsedUrl['pass'])) {
                $cleanUrl .= ':' . $parsedUrl['pass'];
            }
            $cleanUrl .= '@';
        }

        if (isset($parsedUrl['host'])) {
            $cleanUrl .= $parsedUrl['host'];
        }

        if (isset($parsedUrl['port'])) {
            $cleanUrl .= ':' . $parsedUrl['port'];
        }

        if (isset($parsedUrl['path'])) {
            $cleanUrl .= $parsedUrl['path'];
        }

        return $cleanUrl;
    }


    
    private function assignCategoriesToProduct($productId, $topics) {
        foreach ($topics as $topicEdge) {
            $topic = $topicEdge['node'];
            $categoryName = $topic['name'] ?? '';

            if (empty($categoryName)) {
                continue;
            }

            // Add category if it doesn't exist
            $categoryId = $this->categoryManager->getCategoryId($categoryName);
            if (!$categoryId) {
                $categoryId = $this->categoryManager->addCategory($categoryName);
            }

            if ($categoryId) {
                // Link product to category
                $stmt = $this->pdo->prepare("INSERT IGNORE INTO product_categories (product_id, category_id) VALUES (?, ?)");
                $stmt->execute([$productId, $categoryId]);
            }
        }
    }



    public function displayResults($results) {
        echo "\n📊 Processing Results:\n";
        echo "- Products inserted: {$results['inserted']}\n";
        echo "- Products updated: {$results['updated']}\n";
        echo "- Errors: {$results['errors']}\n";
        echo "- Total processed: " . ($results['inserted'] + $results['updated'] + $results['errors']) . "\n";
    }

    public function checkRateLimit() {
        // Make a minimal API call to check rate limit status
        $query = [
            'query' => '{ viewer { user { id } } }',
            'variables' => []
        ];

        try {
            $response = $this->makeHttpRequest($query);

            if (isset($response['errors'])) {
                foreach ($response['errors'] as $error) {
                    if (strpos($error['message'], 'rate limit') !== false ||
                        strpos($error['message'], 'Rate limit') !== false) {

                        // Try to extract reset time from error message
                        if (preg_match('/(\d+)\s*seconds?/i', $error['message'], $matches)) {
                            $resetTime = intval($matches[1]);
                            echo "Rate limit reached. Reset in $resetTime seconds.\n";
                        } else {
                            echo "Rate limit reached. Try again later.\n";
                        }
                        return;
                    }
                }

                // Other API error
                echo "API error: " . $response['errors'][0]['message'] . "\n";
                return;
            }

            // Check rate limit headers if available
            if (!empty($this->rateLimitInfo)) {
                $remaining = intval($this->rateLimitInfo['remaining'] ?? 0);
                $limit = intval($this->rateLimitInfo['limit'] ?? 0);
                $reset = intval($this->rateLimitInfo['reset'] ?? 0);

                // Display current rate limit status
                if ($limit > 0) {
                    $resetTime = date('H:i:s', $reset);
                    $resetInSeconds = max(0, $reset - time());
                    echo "Rate limit: $remaining/$limit remaining (resets at $resetTime, reset_in: $resetInSeconds seconds)\n";
                }

                // Check if we're actually rate limited (very few credits left or none)
                if ($remaining <= 10) { // Conservative threshold
                    if ($reset > time()) {
                        $waitTime = $reset - time();
                        echo "Rate limit reached. Reset in $waitTime seconds.\n";
                        return;
                    } else {
                        echo "Rate limit reset time has passed. Reset in 0 seconds.\n";
                        return;
                    }
                }
            }

            // No rate limit detected
            echo "API credits available - no rate limit detected.\n";

        } catch (Exception $e) {
            if (strpos($e->getMessage(), 'rate limit') !== false) {
                echo "Rate limit reached. " . $e->getMessage() . "\n";
            } else {
                echo "API check failed: " . $e->getMessage() . "\n";
            }
        }
    }

    private function makeHttpRequest($query) {
        $headers = [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $this->apiKey,
            'User-Agent: ProductHunt-Fetcher/1.0',
            'Accept: application/json',
            'Host: api.producthunt.com'
        ];

        $responseHeaders = [];

        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $this->apiUrl,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($query),
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_SSL_VERIFYPEER => true,
            CURLOPT_HEADERFUNCTION => function($curl, $header) use (&$responseHeaders) {
                unset($curl);
                $len = strlen($header);
                $header = explode(':', $header, 2);
                if (count($header) < 2) return $len;

                $name = strtolower(trim($header[0]));
                $value = trim($header[1]);
                $responseHeaders[$name] = $value;

                return $len;
            }
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        // Store rate limit information
        $this->rateLimitInfo = [
            'limit' => $responseHeaders['x-ratelimit-limit'] ??
                      $responseHeaders['x-rate-limit-limit'] ?? 0,
            'remaining' => $responseHeaders['x-ratelimit-remaining'] ??
                          $responseHeaders['x-rate-limit-remaining'] ?? 0,
            'reset' => $responseHeaders['x-ratelimit-reset'] ??
                      $responseHeaders['x-rate-limit-reset'] ?? 0
        ];

        if (curl_error($ch)) {
            $error = curl_error($ch);
            curl_close($ch);
            throw new Exception("cURL error: $error");
        }

        curl_close($ch);

        if ($httpCode !== 200) {
            throw new Exception("API request failed with HTTP code: $httpCode. Response: $response");
        }

        $data = json_decode($response, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception("Invalid JSON response: " . json_last_error_msg());
        }

        return $data;
    }

    private function getTargetDate() {
        if ($this->targetDate) {
            return $this->targetDate;
        } elseif ($this->startDate) {
            return $this->startDate; // For date ranges, use start date as key
        } else {
            return date('Y-m-d');
        }
    }

    private function getExistingProgress() {
        $targetDate = $this->getTargetDate();

        $stmt = $this->pdo->prepare("
            SELECT * FROM fetch_progress
            WHERE target_date = ?
            AND status IN ('in_progress', 'rate_limited')
            ORDER BY updated_at DESC
            LIMIT 1
        ");
        $stmt->execute([$targetDate]);
        return $stmt->fetch();
    }

    private function initializeProgress() {
        $targetDate = $this->getTargetDate();

        // Clear any existing progress for this date
        $stmt = $this->pdo->prepare("DELETE FROM fetch_progress WHERE target_date = ?");
        $stmt->execute([$targetDate]);

        // Create new progress record
        $stmt = $this->pdo->prepare("
            INSERT INTO fetch_progress (target_date, pagination_cursor, page_count, products_fetched, status)
            VALUES (?, NULL, 0, 0, 'in_progress')
        ");
        $stmt->execute([$targetDate]);
    }

    private function updateProgress($cursor, $pageCount, $productsFetched, $status) {
        $targetDate = $this->getTargetDate();

        $stmt = $this->pdo->prepare("
            UPDATE fetch_progress
            SET pagination_cursor = ?, page_count = ?, products_fetched = ?, status = ?, updated_at = NOW()
            WHERE target_date = ?
        ");
        $stmt->execute([$cursor, $pageCount, $productsFetched, $status, $targetDate]);
    }
}

// Helper function to parse command line arguments
function parseArguments($argv) {
    $options = [];

    for ($i = 1; $i < count($argv); $i++) {
        $arg = $argv[$i];

        if (strpos($arg, '--date=') === 0) {
            $options['date'] = substr($arg, 7);
        } elseif (strpos($arg, '--start-date=') === 0) {
            $options['start_date'] = substr($arg, 13);
        } elseif (strpos($arg, '--end-date=') === 0) {
            $options['end_date'] = substr($arg, 11);
        } elseif (strpos($arg, '--max-pages=') === 0) {
            $options['max_pages'] = (int)substr($arg, 12);
        } elseif ($arg === '--check-rate-limit') {
            $options['check_rate_limit'] = true;
        } elseif ($arg === '--help' || $arg === '-h') {
            echo "Usage: php fetch_products.php [OPTIONS]\n\n";
            echo "Options:\n";
            echo "  --date=YYYY-MM-DD          Fetch products from specific date\n";
            echo "  --start-date=YYYY-MM-DD    Fetch products from date range (requires --end-date)\n";
            echo "  --end-date=YYYY-MM-DD      Fetch products to date range (requires --start-date)\n";
            echo "  --max-pages=N              Limit fetching to N pages (for testing)\n";
            echo "  --help, -h                 Show this help message\n\n";
            echo "Examples:\n";
            echo "  php fetch_products.php                    # Fetch today's products\n";
            echo "  php fetch_products.php --date=2024-01-15  # Fetch products from Jan 15, 2024\n";
            echo "  php fetch_products.php --start-date=2024-01-01 --end-date=2024-01-31  # Fetch January 2024\n";
            exit(0);
        }
    }

    return $options;
}

// Run the fetcher if this file is executed directly
if (basename(__FILE__) == basename($_SERVER["SCRIPT_NAME"])) {
    $startTime = microtime(true);
    $startDateTime = date('Y-m-d H:i:s');

    $options = parseArguments($argv ?? []);

    // Handle rate limit check
    if (isset($options['check_rate_limit'])) {
        try {
            $fetcher = new ProductHuntAPI($options);
            $fetcher->checkRateLimit();
            exit(0);
        } catch (Exception $e) {
            echo "Rate limit check failed: " . $e->getMessage() . "\n";
            exit(1);
        }
    }

    echo "🚀 Starting Product Hunt data fetch...\n";
    echo "⏰ Start time: $startDateTime\n";

    // Display date filter info
    if (isset($options['date'])) {
        echo "📅 Fetching products from: {$options['date']}\n\n";
    } elseif (isset($options['start_date']) && isset($options['end_date'])) {
        echo "📅 Fetching products from: {$options['start_date']} to {$options['end_date']}\n\n";
    } else {
        echo "📅 Fetching today's products (" . date('Y-m-d') . ")\n\n";
    }

    try {
        $fetcher = new ProductHuntAPI($options);

        // Normal processing with API calls
        echo "📡 Fetching products from Product Hunt API...\n";
        $products = $fetcher->fetchAllTodaysPosts();

        if (empty($products)) {
            echo "ℹ️  No products found for the specified date(s).\n";
            exit(0);
        }

        echo "📋 Processing " . count($products) . " products...\n\n";

        echo "💾 Saving products to database...\n";
        $results = $fetcher->saveProductsToDatabase($products);

        $fetcher->displayResults($results);



        $endTime = microtime(true);
        $endDateTime = date('Y-m-d H:i:s');
        $duration = $endTime - $startTime;
        $durationFormatted = gmdate('H:i:s', (int)$duration);

        echo "\n⏰ End time: $endDateTime\n";
        echo "⏱️  Total duration: $durationFormatted (" . number_format($duration, 2) . " seconds)\n";
        echo "\n✨ Product Hunt data fetch completed!\n";

    } catch (Exception $e) {
        $endTime = microtime(true);
        $endDateTime = date('Y-m-d H:i:s');
        $duration = $endTime - $startTime;
        $durationFormatted = gmdate('H:i:s', (int)$duration);

        echo "\n⏰ End time: $endDateTime\n";
        echo "⏱️  Total duration: $durationFormatted (" . number_format($duration, 2) . " seconds)\n";
        echo "❌ Error: " . $e->getMessage() . "\n";
        exit(1);
    }
}
?>
