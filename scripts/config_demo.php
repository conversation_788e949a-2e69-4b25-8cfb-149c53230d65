<?php
/**
 * Configuration Demo Script
 * Shows how the flexible scheduling system works
 */

require_once __DIR__ . '/../config/config.php';

echo "🔧 Fetch Schedule Configuration Demo\n";
echo "====================================\n\n";

// Load configuration
$configFile = __DIR__ . '/../config/fetch_schedule.json';
if (!file_exists($configFile)) {
    echo "❌ Configuration file not found: $configFile\n";
    exit(1);
}

$configContent = file_get_contents($configFile);
$config = json_decode($configContent, true);

if (json_last_error() !== JSON_ERROR_NONE) {
    echo "❌ Invalid JSON in configuration file\n";
    exit(1);
}

echo "✅ Configuration loaded successfully\n\n";

// Display current settings
echo "📋 Current Settings:\n";
echo "- Timezone: " . ($config['settings']['timezone'] ?? 'UTC') . "\n";
echo "- Check interval: " . ($config['settings']['check_interval_minutes'] ?? 5) . " minutes\n";
echo "- Rate limit buffer: " . ($config['settings']['rate_limit_buffer_seconds'] ?? 5) . " seconds\n\n";

// Display daily schedule
echo "📅 Daily Schedule (Pacific Time):\n";
foreach ($config['daily_schedule'] as $schedule) {
    echo "✅ {$schedule['time']} PT:\n";
    echo "   - Target: " . ($schedule['target'] ?? 'unknown') . "\n";
    echo "   - Description: " . ($schedule['description'] ?? 'No description') . "\n\n";
}

// Show current time and active rule
$timezone = new DateTimeZone($config['settings']['timezone'] ?? 'America/Los_Angeles');
$now = new DateTime('now', $timezone);
$currentTime = $now->format('H:i');
echo "🕐 Current time in PT: $currentTime\n\n";

// Show how to modify configuration
echo "🛠️  How to Modify Configuration:\n";
echo "1. Edit config/fetch_schedule.json\n";
echo "2. Modify the 'daily_schedule' array to change times and targets\n";
echo "3. Adjust 'settings' for timing parameters\n";
echo "4. All times are in Pacific Time (PT)\n\n";

echo "📝 Example modifications:\n";
echo "- Change schedule times: '06:00' to '05:30' for earlier morning fetch\n";
echo "- Change check_interval_minutes from 5 to 10 for less frequent daemon checks\n";
echo "- Add more schedule entries for additional fetch times\n\n";

echo "🔄 To apply changes: Restart the continuous_fetch.php daemon\n";
?>
