<?php
/**
 * Product Hunt URL Resolver
 * Resolves external URLs for products in the database
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../config/database_setup.php';
require_once __DIR__ . '/../includes/url_utils.php';

class URLResolver {
    private $pdo;
    private $options = [];
    private $targetDate = null;
    private $startDate = null;
    private $endDate = null;
    private $limit = null;
    private $force = false;
    private $all = false;
    private $recheckDown = false;
    private $recheckEmpty = false;
    private $usePlaywright = true; // Default to true for fallback
    private $playwrightAvailable = false;
    private $delayMultiplier = 1.0; // Default 1x multiplier

    public function __construct($options = []) {
        $this->options = $options;
        validateConfig();
        $this->connectToDatabase();

        // Set date filters
        if (isset($options['date'])) {
            $this->targetDate = $options['date'];
        } elseif (isset($options['start_date']) && isset($options['end_date'])) {
            $this->startDate = $options['start_date'];
            $this->endDate = $options['end_date'];
        }

        // Set other options
        $this->force = isset($options['force']) && $options['force'];
        $this->all = isset($options['all']) && $options['all'];
        $this->recheckDown = isset($options['recheck_down']) && $options['recheck_down'];
        $this->recheckEmpty = isset($options['recheck_empty']) && $options['recheck_empty'];

        if (isset($options['limit']) && $options['limit'] > 0) {
            $this->limit = $options['limit'];
            echo "🔢 Limiting URL resolution to {$this->limit} products\n";
        }

        // Set delay multiplier
        if (isset($options['delay_multiplier']) && $options['delay_multiplier'] > 0) {
            $this->delayMultiplier = floatval($options['delay_multiplier']);
            echo "⏱️  Using delay multiplier: {$this->delayMultiplier}x\n";
        } else {
            echo "⏱️  Using default delay multiplier: {$this->delayMultiplier}x\n";
        }

        // Always check Playwright availability for fallback
        $this->checkPlaywrightAvailability();
    }
    
    private function connectToDatabase() {
        try {
            $dsn = "mysql:host=" . DB_HOST . ";port=" . DB_PORT . ";dbname=" . DB_NAME . ";charset=utf8mb4";
            $this->pdo = new PDO($dsn, DB_USER, DB_PASSWORD, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
            ]);
        } catch (PDOException $e) {
            throw new Exception("Database connection failed: " . $e->getMessage());
        }
    }

    private function checkPlaywrightAvailability() {
        // Check if Node.js is available
        $nodeCheck = shell_exec('which node 2>/dev/null');
        if (empty($nodeCheck)) {
            echo "⚠️  Node.js not found. Playwright fallback disabled.\n";
            $this->playwrightAvailable = false;
            return;
        }
        // Check if npm is available
        $npmCheck = shell_exec('which npm 2>/dev/null');
        if (empty($npmCheck)) {
            echo "⚠️  npm not found. Playwright fallback disabled.\n";
            $this->playwrightAvailable = false;
            return;
        }


        // Check if package.json exists, create if not
        if (!file_exists('package.json')) {
            echo "� Creating package.json...\n";
            $this->createPackageJson();
        }

        // Check if Playwright is installed

        if (!$this->isPlaywrightInstalled()) {
            echo "⚠️  Playwright not found. Installing...\n";
            $this->installPlaywright();
        } else {

            $this->playwrightAvailable = true;
        }

    }

    private function createPackageJson() {
        $packageJson = [
            'name' => 'product-hunt-url-resolver',
            'version' => '1.0.0',
            'description' => 'URL resolver with Playwright fallback',
            'private' => true,
            'dependencies' => []
        ];

        file_put_contents('package.json', json_encode($packageJson, JSON_PRETTY_PRINT));
        echo "✅ package.json created\n";
    }

    private function isPlaywrightInstalled() {
        // Check if playwright is in node_modules
        return file_exists('node_modules/playwright/package.json');
    }

    private function installPlaywright() {
        echo "📦 Installing Playwright...\n";

        // Install Playwright using npm
        $installCmd = 'npm install playwright 2>&1';
        echo "Running: $installCmd\n";
        $output = shell_exec($installCmd);

        if (strpos($output, 'error') !== false || strpos($output, 'Error') !== false) {
            echo "❌ Failed to install Playwright package. Output:\n$output\n";
            $this->playwrightAvailable = false;
            return;
        }

        echo "✅ Playwright package installed\n";

        // Install Playwright browsers
        echo "📦 Installing Playwright browsers...\n";
        $browsersCmd = 'npx playwright install chromium 2>&1';
        echo "Running: $browsersCmd\n";
        $browsersOutput = shell_exec($browsersCmd);

        if (strpos($browsersOutput, 'error') !== false || strpos($browsersOutput, 'Error') !== false) {
            echo "❌ Failed to install Playwright browsers. Output:\n$browsersOutput\n";
            $this->playwrightAvailable = false;
        } else {
            echo "✅ Playwright browsers installed successfully\n";
            $this->playwrightAvailable = true;
        }
    }

    private function resolveUrlWithPlaywright($url) {
        if (!$this->playwrightAvailable) {
            return ['success' => false, 'error' => 'Playwright not available'];
        }

        echo "🎭 Using Playwright fallback for: $url\n";

        // Create a temporary JavaScript file for Playwright in the project directory
        $jsScript = $this->createPlaywrightScript($url);
        $tempFile = 'temp_playwright_' . uniqid() . '.js';
        file_put_contents($tempFile, $jsScript);

        try {
            // Run the script directly with Node.js
            $cmd = "node " . escapeshellarg($tempFile) . " 2>&1";

            $output = shell_exec($cmd);

            // Parse the result
            $result = $this->parsePlaywrightOutput($output);

            return $result;

        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        } finally {
            // Clean up temp file
            if (file_exists($tempFile)) {
                unlink($tempFile);
            }
        }
    }

    private function createPlaywrightScript($url) {
        return "
const { chromium } = require('playwright');

(async () => {
    let browser;
    try {
        // Launch browser
        browser = await chromium.launch({ headless: true });
        const context = await browser.newContext({
            userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            viewport: { width: 1920, height: 1080 },
            ignoreHTTPSErrors: true
        });
        const page = await context.newPage();

        // Navigate to URL with timeout
        const response = await page.goto('" . addslashes($url) . "', {
            waitUntil: 'networkidle',
            timeout: 30000
        });

        // Get final URL after redirects
        const finalUrl = page.url();
        const statusCode = response.status();

        // Determine status
        const status = (statusCode >= 200 && statusCode < 400) ? 'up' : 'down';

        // Output result as JSON
        console.log(JSON.stringify({
            success: true,
            finalUrl: finalUrl,
            statusCode: statusCode,
            status: status
        }));

    } catch (error) {
        console.log(JSON.stringify({
            success: false,
            error: error.message
        }));
    } finally {
        if (browser) {
            await browser.close();
        }
    }
})();
";
    }

    private function parsePlaywrightOutput($output) {
        // Look for JSON output in the Playwright output
        $lines = explode("\n", $output);

        foreach ($lines as $line) {
            $line = trim($line);
            if (strpos($line, '{') === 0) {
                $json = json_decode($line, true);
                if ($json !== null) {
                    return $json;
                }
            }
        }

        // If no JSON found, try to extract URL from error messages
        $finalUrl = $this->extractUrlFromPlaywrightError($output);
        if ($finalUrl) {
            return [
                'success' => true,
                'finalUrl' => $finalUrl,
                'status' => 'down',
                'error' => 'Extracted from error message'
            ];
        }

        return [
            'success' => false,
            'error' => 'Playwright failed to resolve URL'
        ];
    }

    private function extractUrlFromPlaywrightError($output) {
        // Try to extract URL from common Playwright error patterns
        $patterns = [
            '/page\.goto: [^"]*at (https?:\/\/[^\s]+)/',
            '/navigating to "(https?:\/\/[^"]+)"/',
            '/net::ERR_[A-Z_]+ at (https?:\/\/[^\s]+)/',
        ];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $output, $matches)) {
                $url = $matches[1];
                // Clean up any trailing characters
                $url = rtrim($url, '.,;');

                return $url;
            }
        }

        return null;
    }



    public function resolveUrls() {
        $products = $this->getProductsToResolve();
        
        if (empty($products)) {
            echo "ℹ️  No products found that need URL resolution.\n";
            return 0;
        }

        echo "Found " . count($products) . " products that need URL resolution:\n\n";

        $processed = 0;
        $successful = 0;
        $failed = 0;

        foreach ($products as $product) {
            echo "🔗 Processing: " . $product['name'] . "\n";

            // Update the updated_at timestamp and set url_checked flag
            $updateStmt = $this->pdo->prepare("UPDATE products SET updated_at = NOW(), url_checked = 1 WHERE id = ?");
            $updateStmt->execute([$product['id']]);

            // Process URL resolution
            if ($product['url']) {
                $result = $this->resolveExternalUrl($product['id']);
                if ($result) {
                    $successful++;
                } else {
                    $failed++;
                }
                $processed++;
            }
        }

        echo "\n📊 URL Resolution Results:\n";
        echo "- Products processed: {$processed}\n";
        echo "- Successfully resolved: {$successful}\n";
        echo "- Failed to resolve: {$failed}\n";

        return $processed;
    }

    private function getProductsToResolve() {
        $whereConditions = [];
        $params = [];

        // Date filtering
        if ($this->targetDate) {
            $whereConditions[] = "DATE(date_created) = ?";
            $params[] = $this->targetDate;
        } elseif ($this->startDate && $this->endDate) {
            $whereConditions[] = "DATE(date_created) BETWEEN ? AND ?";
            $params[] = $this->startDate;
            $params[] = $this->endDate;
        }

        // URL resolution filtering
        if ($this->force) {
            // Force mode: resolve all URLs (no additional filtering)
        } elseif ($this->recheckDown) {
            // Recheck down mode: only resolve products where external_url_status is 'down'
            $whereConditions[] = "external_url_status = 'down'";
        } elseif ($this->recheckEmpty) {
            // Recheck empty mode: only resolve products where external_url is null (regardless of status)
            $whereConditions[] = "external_url IS NULL";
        } else {
            // Default: only resolve products where both external_url and external_url_status are null AND url_checked is null
            $whereConditions[] = "external_url IS NULL AND external_url_status IS NULL AND url_checked IS NULL";
        }

        // Build the WHERE clause
        $whereClause = '';
        if (!empty($whereConditions)) {
            $whereClause = 'WHERE ' . implode(' AND ', $whereConditions);
        }

        // Order by clause
        $orderClause = $this->all ? 
            'ORDER BY date_created DESC, votes_count DESC' : 
            'ORDER BY date_created DESC, votes_count DESC';

        // Limit clause
        $limitClause = $this->limit ? "LIMIT {$this->limit}" : '';

        $sql = "
            SELECT id, name, url, external_url, external_url_status
            FROM products
            {$whereClause}
            {$orderClause}
            {$limitClause}
        ";

        $stmt = $this->pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll();
    }

    private function resolveExternalUrl($productId) {
        try {
            // Get the current URL for this product
            $stmt = $this->pdo->prepare("SELECT url FROM products WHERE id = ?");
            $stmt->execute([$productId]);
            $result = $stmt->fetch();

            if (!$result || empty($result['url'])) {
                return false; // No URL to resolve
            }

            $originalUrl = $result['url'];

            // Check if this URL is likely a redirect (skip this check in recheck modes)
            if (!$this->recheckDown && !$this->recheckEmpty && !URLUtils::isLikelyRedirect($originalUrl)) {
                // Not a redirect URL, mark as valid and store cleaned original URL
                $cleanedUrl = URLUtils::removeUrlParameters($originalUrl);
                $stmt = $this->pdo->prepare("UPDATE products SET external_url = ?, external_url_status = 'up' WHERE id = ?");
                $stmt->execute([$cleanedUrl, $productId]);

                // Remove any review records since status is now 'up'
                $cleanupStmt = $this->pdo->prepare("DELETE FROM product_reviews WHERE product_id = ?");
                $cleanupStmt->execute([$productId]);

                echo "✅ Direct URL (no redirect): " . substr($cleanedUrl, 0, 50) . "...\n";
                return true;
            }

            // Follow redirects to get the final URL
            echo "🔗 Resolving redirects for: " . substr($originalUrl, 0, 50) . "...\n";
            $urlResult = URLUtils::getFinalUrlWithStatus($originalUrl);

            if (!empty($urlResult['url'])) {
                // We got a final URL - check if we should try Playwright fallback for 'down' status
                $cleanedFinalUrl = URLUtils::removeUrlParameters($urlResult['url']);
                $status = $urlResult['status'];

                if ($status === 'up') {
                    // URL is working, save it and return success
                    $stmt = $this->pdo->prepare("UPDATE products SET external_url = ?, external_url_status = ? WHERE id = ?");
                    $stmt->execute([$cleanedFinalUrl, $status, $productId]);

                    // Remove any review records since status is now 'up'
                    $cleanupStmt = $this->pdo->prepare("DELETE FROM product_reviews WHERE product_id = ?");
                    $cleanupStmt->execute([$productId]);

                    echo "✅ Resolved to: " . substr($cleanedFinalUrl, 0, 50) . "...\n";
                    return true;
                } else {
                    // URL is down - try Playwright fallback if available before giving up
                    if ($this->usePlaywright && $this->playwrightAvailable) {
                        echo "❌ cURL reports URL is down, trying Playwright fallback...\n";

                        // Add delay to be respectful (with multiplier)
                        $minDelay = intval(6 * $this->delayMultiplier);
                        $maxDelay = intval(12 * $this->delayMultiplier);
                        sleep(rand($minDelay, $maxDelay));

                        $playwrightResult = $this->resolveUrlWithPlaywright($originalUrl);

                        if ($playwrightResult['success'] && isset($playwrightResult['finalUrl'])) {
                            // Playwright succeeded - use its result
                            $playwrightCleanedUrl = URLUtils::removeUrlParameters($playwrightResult['finalUrl']);
                            $playwrightStatus = $playwrightResult['status'] ?? 'down';
                            $stmt = $this->pdo->prepare("UPDATE products SET external_url = ?, external_url_status = ? WHERE id = ?");
                            $stmt->execute([$playwrightCleanedUrl, $playwrightStatus, $productId]);

                            if ($playwrightStatus === 'up') {
                                // Remove any review records since status is now 'up'
                                $cleanupStmt = $this->pdo->prepare("DELETE FROM product_reviews WHERE product_id = ?");
                                $cleanupStmt->execute([$productId]);

                                echo "✅ Playwright resolved to: " . substr($playwrightCleanedUrl, 0, 50) . "...\n";
                                return true;
                            } else {
                                echo "❌ Playwright also found URL is down: " . substr($playwrightCleanedUrl, 0, 50) . "...\n";
                                return false;
                            }
                        } else {
                            // Playwright failed - fall back to cURL result
                            echo "⚠️ Playwright fallback failed, using cURL result\n";
                            $stmt = $this->pdo->prepare("UPDATE products SET external_url = ?, external_url_status = ? WHERE id = ?");
                            $stmt->execute([$cleanedFinalUrl, $status, $productId]);
                            echo "❌ Resolved to final URL but it's down: " . substr($cleanedFinalUrl, 0, 50) . "...\n";
                            return false;
                        }
                    } else {
                        // No Playwright fallback available - use cURL result
                        $stmt = $this->pdo->prepare("UPDATE products SET external_url = ?, external_url_status = ? WHERE id = ?");
                        $stmt->execute([$cleanedFinalUrl, $status, $productId]);
                        echo "❌ Resolved to final URL but it's down: " . substr($cleanedFinalUrl, 0, 50) . "...\n";
                        return false;
                    }
                }
            } else {
                // cURL completely failed - try Playwright fallback if available
                if ($this->usePlaywright && $this->playwrightAvailable) {
                    echo "🎭 Trying Playwright fallback...\n";

                    // Add delay to be respectful (with multiplier)
                    $minDelay = intval(6 * $this->delayMultiplier);
                    $maxDelay = intval(12 * $this->delayMultiplier);
                    sleep(rand($minDelay, $maxDelay));

                    $playwrightResult = $this->resolveUrlWithPlaywright($originalUrl);

                    if ($playwrightResult['success'] && isset($playwrightResult['finalUrl'])) {
                        // Always save the final URL from Playwright, regardless of status
                        $cleanedFinalUrl = URLUtils::removeUrlParameters($playwrightResult['finalUrl']);
                        $status = $playwrightResult['status'] ?? 'down';
                        $stmt = $this->pdo->prepare("UPDATE products SET external_url = ?, external_url_status = ? WHERE id = ?");
                        $stmt->execute([$cleanedFinalUrl, $status, $productId]);

                        if ($status === 'up') {
                            // Remove any review records since status is now 'up'
                            $cleanupStmt = $this->pdo->prepare("DELETE FROM product_reviews WHERE product_id = ?");
                            $cleanupStmt->execute([$productId]);

                            echo "✅ Playwright resolved to: " . substr($cleanedFinalUrl, 0, 50) . "...\n";
                            return true;
                        } else {
                            echo "❌ Playwright found final URL but it's down: " . substr($cleanedFinalUrl, 0, 50) . "...\n";
                            return false;
                        }
                    } else {
                        echo "⚠️ Playwright fallback failed: " . ($playwrightResult['error'] ?? 'Unknown error') . "\n";

                        // Even if Playwright failed, check if we can extract a URL from the error
                        if (isset($playwrightResult['finalUrl']) && !empty($playwrightResult['finalUrl'])) {
                            $cleanedFinalUrl = URLUtils::removeUrlParameters($playwrightResult['finalUrl']);
                            $stmt = $this->pdo->prepare("UPDATE products SET external_url = ?, external_url_status = 'down' WHERE id = ?");
                            $stmt->execute([$cleanedFinalUrl, $productId]);
                            echo "❌ Saved final URL from Playwright error: " . substr($cleanedFinalUrl, 0, 50) . "...\n";
                            return false;
                        }
                    }
                }

                // Failed to resolve completely - keep external_url as NULL
                $stmt = $this->pdo->prepare("UPDATE products SET external_url = NULL, external_url_status = 'down' WHERE id = ?");
                $stmt->execute([$productId]);
                echo "❌ Failed to resolve URL completely - no final URL found\n";
                return false;
            }

        } catch (Exception $e) {
            echo "⚠️ Error resolving URL for product $productId: " . $e->getMessage() . "\n";
            return false;
        }
    }
}

// Helper function to parse command line arguments
function parseArguments($argv) {
    $options = [];

    for ($i = 1; $i < count($argv); $i++) {
        $arg = $argv[$i];

        if (strpos($arg, '--date=') === 0) {
            $options['date'] = substr($arg, 7);
        } elseif (strpos($arg, '--start-date=') === 0) {
            $options['start_date'] = substr($arg, 13);
        } elseif (strpos($arg, '--end-date=') === 0) {
            $options['end_date'] = substr($arg, 11);
        } elseif (strpos($arg, '--limit=') === 0) {
            $options['limit'] = (int)substr($arg, 8);
        } elseif (strpos($arg, '--delay-multiplier=') === 0) {
            $options['delay_multiplier'] = (float)substr($arg, 19);
        } elseif ($arg === '--force') {
            $options['force'] = true;
        } elseif ($arg === '--all') {
            $options['all'] = true;
        } elseif ($arg === '--recheck-down') {
            $options['recheck_down'] = true;
        } elseif ($arg === '--recheck-empty') {
            $options['recheck_empty'] = true;
        } elseif ($arg === '--help' || $arg === '-h') {
            echo "Usage: php resolve_urls.php [OPTIONS]\n\n";
            echo "Options:\n";
            echo "  --date=YYYY-MM-DD          Resolve URLs for products from specific date\n";
            echo "  --start-date=YYYY-MM-DD    Resolve URLs for products from date range (requires --end-date)\n";
            echo "  --end-date=YYYY-MM-DD      Resolve URLs for products to date range (requires --start-date)\n";
            echo "  --limit=N                  Limit URL resolution to N products\n";
            echo "  --force                    Resolve all URLs (not just unresolved ones)\n";
            echo "  --recheck-down             Recheck URLs that previously failed (status = 'down')\n";
            echo "  --recheck-empty            Recheck URLs with null external_url (regardless of status)\n";
            echo "  --all                      Resolve URLs by created date descending (from newest to oldest)\n";
            echo "  --delay-multiplier=X       Multiply delay range by X (default: 1.0)\n";
            echo "  --help, -h                 Show this help message\n\n";
            echo "Note: Playwright fallback is automatically used when cURL fails to resolve URLs.\n\n";
            echo "Examples:\n";
            echo "  php resolve_urls.php                           # Resolve completely unresolved URLs (default)\n";
            echo "  php resolve_urls.php --date=2024-01-15         # Resolve URLs for products from Jan 15, 2024\n";
            echo "  php resolve_urls.php --recheck-down --limit=25 # Recheck 25 URLs that previously failed\n";
            echo "  php resolve_urls.php --recheck-empty --limit=50 # Recheck 50 URLs with null external_url\n";
            echo "  php resolve_urls.php --force --limit=50        # Force resolve 50 URLs (including already resolved)\n";
            echo "  php resolve_urls.php --delay-multiplier=3.0    # Use 3x delay (18-36 seconds) for gentle operation\n";
            echo "  php resolve_urls.php --delay-multiplier=0.5    # Use 0.5x delay (3-6 seconds) for faster operation\n";
            echo "  php resolve_urls.php --all --limit=100         # Resolve 100 URLs starting from newest products\n";
            echo "  php resolve_urls.php --start-date=2024-01-01 --end-date=2024-01-31  # Resolve URLs for January 2024\n";
            exit(0);
        }
    }

    return $options;
}

// Run the URL resolver if this file is executed directly
if (basename(__FILE__) == basename($_SERVER["SCRIPT_NAME"])) {
    $startTime = microtime(true);
    $startDateTime = date('Y-m-d H:i:s');

    $options = parseArguments($argv ?? []);

    echo "🔗 Starting Product Hunt URL resolution...\n";
    echo "⏰ Start time: $startDateTime\n";

    // Display filtering info
    if (isset($options['date'])) {
        echo "📅 Resolving URLs for products from: {$options['date']}\n";
    } elseif (isset($options['start_date']) && isset($options['end_date'])) {
        echo "📅 Resolving URLs for products from: {$options['start_date']} to {$options['end_date']}\n";
    } else {
        echo "📅 Resolving URLs for all products\n";
    }

    if (isset($options['force']) && $options['force']) {
        echo "🔄 Force mode: Resolving all URLs (including already resolved)\n";
    } elseif (isset($options['recheck_down']) && $options['recheck_down']) {
        echo "🔍 Recheck mode: Only rechecking URLs that previously failed (status = 'down')\n";
    } elseif (isset($options['recheck_empty']) && $options['recheck_empty']) {
        echo "🔄 Recheck empty mode: Only rechecking URLs with null external_url\n";
    } else {
        echo "🎯 Default mode: Only resolving completely unresolved URLs (both external_url and status are null)\n";
    }

    if (isset($options['all']) && $options['all']) {
        echo "📊 Processing products from newest to oldest\n";
    }

    echo "\n";

    try {
        $resolver = new URLResolver($options);
        $processed = $resolver->resolveUrls();

        $endTime = microtime(true);
        $endDateTime = date('Y-m-d H:i:s');
        $duration = $endTime - $startTime;
        $durationFormatted = gmdate('H:i:s', (int)$duration);

        echo "\n⏰ End time: $endDateTime\n";
        echo "⏱️  Total duration: $durationFormatted (" . number_format($duration, 2) . " seconds)\n";
        echo "\n✨ URL resolution completed!\n";

    } catch (Exception $e) {
        $endTime = microtime(true);
        $endDateTime = date('Y-m-d H:i:s');
        $duration = $endTime - $startTime;
        $durationFormatted = gmdate('H:i:s', (int)$duration);

        echo "\n⏰ End time: $endDateTime\n";
        echo "⏱️  Total duration: $durationFormatted (" . number_format($duration, 2) . " seconds)\n";
        echo "❌ Error: " . $e->getMessage() . "\n";
        exit(1);
    }
}
?>
