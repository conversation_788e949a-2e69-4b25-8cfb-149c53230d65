<?php
/**
 * Debug script to trace rating data storage
 */

require_once __DIR__ . '/../../config/config.php';

echo "🔍 Debug Rating Data Storage\n";
echo "============================\n\n";

// Simulate the API response data
$testProduct = [
    'id' => 'test-123',
    'name' => 'Test Product',
    'tagline' => 'Test tagline',
    'description' => 'Test description',
    'votesCount' => 100,
    'reviewsRating' => 0,      // API returns 0
    'reviewsCount' => 0,       // API returns 0
    'url' => 'https://example.com',
    'website' => 'https://example.com',
    'createdAt' => '2025-09-01T10:00:00Z'
];

echo "📊 Test Product Data (simulating API response):\n";
foreach ($testProduct as $key => $value) {
    $displayValue = is_null($value) ? 'NULL' : (is_string($value) ? "\"$value\"" : $value);
    echo "   $key: $displayValue\n";
}

echo "\n🔧 Testing PHP Null Coalescing Operator:\n";

// Test the exact expressions used in the code
$reviewsRating = $testProduct['reviewsRating'] ?? null;
$reviewsCount = $testProduct['reviewsCount'] ?? null;
$votesCount = $testProduct['votesCount'] ?? 0;

echo "   \$testProduct['reviewsRating'] ?? null = " . (is_null($reviewsRating) ? 'NULL' : $reviewsRating) . "\n";
echo "   \$testProduct['reviewsCount'] ?? null = " . (is_null($reviewsCount) ? 'NULL' : $reviewsCount) . "\n";
echo "   \$testProduct['votesCount'] ?? 0 = $votesCount\n";

echo "\n📋 Testing Database Insert:\n";

try {
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
        DB_USER,
        DB_PASSWORD,
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    
    // Clean up any existing test data
    $pdo->exec("DELETE FROM products WHERE id = 'test-123'");
    
    // Test INSERT with the same logic as the real code
    $sql = "INSERT INTO products (
        id, name, tagline, description, votes_count, review_rating,
        review_count, product_url, date_created, url
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    $stmt = $pdo->prepare($sql);
    
    $dateCreated = date('Y-m-d', strtotime($testProduct['createdAt']));
    $websiteUrl = $testProduct['website'];
    $productUrl = $testProduct['url'];
    
    $insertData = [
        $testProduct['id'],
        $testProduct['name'] ?? '',
        $testProduct['tagline'] ?? '',
        $testProduct['description'] ?? '',
        $testProduct['votesCount'] ?? 0,
        $testProduct['reviewsRating'] ?? null,
        $testProduct['reviewsCount'] ?? null,
        $productUrl,
        $dateCreated,
        $websiteUrl
    ];
    
    echo "   Insert data array:\n";
    foreach ($insertData as $i => $value) {
        $displayValue = is_null($value) ? 'NULL' : (is_string($value) ? "\"$value\"" : $value);
        echo "     [$i]: $displayValue\n";
    }
    
    $stmt->execute($insertData);
    echo "   ✅ Insert successful\n";
    
    // Check what was actually stored
    $stmt = $pdo->prepare("SELECT review_rating, review_count, votes_count FROM products WHERE id = ?");
    $stmt->execute(['test-123']);
    $stored = $stmt->fetch();
    
    if ($stored) {
        echo "\n📊 Stored in Database:\n";
        echo "   review_rating: " . (is_null($stored['review_rating']) ? 'NULL' : $stored['review_rating']) . "\n";
        echo "   review_count: " . (is_null($stored['review_count']) ? 'NULL' : $stored['review_count']) . "\n";
        echo "   votes_count: " . (is_null($stored['votes_count']) ? 'NULL' : $stored['votes_count']) . "\n";
    }
    
    // Clean up
    $pdo->exec("DELETE FROM products WHERE id = 'test-123'");
    
} catch (Exception $e) {
    echo "   ❌ Database error: " . $e->getMessage() . "\n";
}

echo "\n🎯 Analysis:\n";
echo "If review_count shows as NULL in database but we passed 0:\n";
echo "1. Check if there's a database constraint or trigger\n";
echo "2. Check if the column has a default value that overrides 0\n";
echo "3. Check if there's application logic that converts 0 to NULL\n";

echo "\n💡 Expected Results:\n";
echo "   review_rating: 0 (or 0.00)\n";
echo "   review_count: 0\n";
echo "   votes_count: 100\n";

echo "\n✅ Debug test completed!\n";
?>
