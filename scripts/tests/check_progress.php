<?php
require_once __DIR__ . '/../../config/config.php';

try {
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
        DB_USER,
        DB_PASSWORD,
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    
    $stmt = $pdo->query("SELECT * FROM fetch_progress ORDER BY updated_at DESC LIMIT 5");
    $records = $stmt->fetchAll();
    
    echo "📊 Recent Fetch Progress:\n";
    foreach ($records as $record) {
        $cursor = $record['pagination_cursor'] ? substr($record['pagination_cursor'], 0, 30) . '...' : 'null';
        echo "Date: {$record['target_date']}\n";
        echo "Pages: {$record['page_count']}, Products: {$record['products_fetched']}\n";
        echo "Status: {$record['status']}\n";
        echo "Cursor: $cursor\n";
        echo "Updated: {$record['updated_at']}\n\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
