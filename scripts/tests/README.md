# Test Scripts Directory

This directory contains all test and debugging scripts for the Product Hunt data fetcher.

## Script Categories

### Test Scripts (test_*.php)
- `test_continuous.php` - Tests continuous fetching functionality
- `test_cursor_persistence.php` - Tests pagination cursor persistence
- `test_rate_limit_*.php` - Various rate limit handling tests
- `test_schedule*.php` - Tests scheduling functionality
- `test_slack.php` - Tests Slack notifications
- `test_specific_product.php` - Tests specific product API calls

### Debug/Utility Scripts
- `check_progress.php` - Check fetch progress status
- `check_ratings.php` - Analyze rating data in database
- `clear_progress.php` - Clear fetch progress (for testing)
- `debug_rating_storage.php` - Debug rating data storage

## Usage

Run tests from the project root:
```bash
php scripts/tests/test_slack.php
php scripts/tests/check_ratings.php
```

All scripts have been updated to use the correct relative paths (../../ instead of ../).
