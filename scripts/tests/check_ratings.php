<?php
require_once __DIR__ . '/../../config/config.php';

try {
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
        DB_USER,
        DB_PASSWORD,
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    
    echo "🔍 Checking Rating Data in Database\n";
    echo "===================================\n\n";
    
    // Check for products with non-zero ratings
    $stmt = $pdo->query("
        SELECT name, review_rating, review_count, votes_count, date_created 
        FROM products 
        WHERE review_rating > 0 OR review_count > 0
        ORDER BY review_rating DESC, review_count DESC 
        LIMIT 10
    ");
    $withRatings = $stmt->fetchAll();
    
    if (empty($withRatings)) {
        echo "❌ No products found with ratings > 0\n";
    } else {
        echo "✅ Products with ratings:\n";
        foreach ($withRatings as $product) {
            echo "   - {$product['name']}: Rating={$product['review_rating']}, Count={$product['review_count']}, Votes={$product['votes_count']} ({$product['date_created']})\n";
        }
    }
    
    echo "\n📊 Rating Statistics:\n";
    
    // Get rating statistics
    $stmt = $pdo->query("
        SELECT 
            COUNT(*) as total_products,
            COUNT(CASE WHEN review_rating > 0 THEN 1 END) as with_ratings,
            COUNT(CASE WHEN review_count > 0 THEN 1 END) as with_review_count,
            AVG(review_rating) as avg_rating,
            MAX(review_rating) as max_rating,
            MAX(review_count) as max_review_count
        FROM products
    ");
    $stats = $stmt->fetch();
    
    echo "   - Total products: " . number_format($stats['total_products']) . "\n";
    echo "   - With ratings > 0: " . number_format($stats['with_ratings']) . "\n";
    echo "   - With review count > 0: " . number_format($stats['with_review_count']) . "\n";
    echo "   - Average rating: " . number_format($stats['avg_rating'], 2) . "\n";
    echo "   - Max rating: " . number_format($stats['max_rating'], 2) . "\n";
    echo "   - Max review count: " . number_format($stats['max_review_count']) . "\n";
    
    echo "\n🎯 Analysis:\n";
    if ($stats['with_ratings'] == 0) {
        echo "   - No products have ratings > 0\n";
        echo "   - This could be normal if:\n";
        echo "     1. Products are too new to have reviews\n";
        echo "     2. Product Hunt changed their rating system\n";
        echo "     3. Ratings are only available for certain products\n";
    } else {
        $percentage = ($stats['with_ratings'] / $stats['total_products']) * 100;
        echo "   - " . number_format($percentage, 1) . "% of products have ratings\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
